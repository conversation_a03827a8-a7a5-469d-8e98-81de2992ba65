@echo off
REM TRON 靓号地址生成器 Windows 构建脚本
REM =======================================

setlocal enabledelayedexpansion

REM 设置颜色
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 默认参数
set "BUILD_TYPE=release"
set "ENABLE_CUDA=false"
set "ENABLE_OPENCL=false"
set "RUN_TESTS=false"
set "CLEAN_BUILD=false"
set "SHOW_HELP=false"

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :args_done
if /i "%~1"=="-h" set "SHOW_HELP=true"
if /i "%~1"=="--help" set "SHOW_HELP=true"
if /i "%~1"=="-c" set "CLEAN_BUILD=true"
if /i "%~1"=="--clean" set "CLEAN_BUILD=true"
if /i "%~1"=="-d" set "BUILD_TYPE=debug"
if /i "%~1"=="--debug" set "BUILD_TYPE=debug"
if /i "%~1"=="-r" set "BUILD_TYPE=release"
if /i "%~1"=="--release" set "BUILD_TYPE=release"
if /i "%~1"=="-t" set "RUN_TESTS=true"
if /i "%~1"=="--test" set "RUN_TESTS=true"
if /i "%~1"=="-g" set "ENABLE_CUDA=true"
if /i "%~1"=="--gpu" set "ENABLE_CUDA=true"
if /i "%~1"=="-o" set "ENABLE_OPENCL=true"
if /i "%~1"=="--opencl" set "ENABLE_OPENCL=true"
if /i "%~1"=="--cpu-only" (
    set "ENABLE_CUDA=false"
    set "ENABLE_OPENCL=false"
)
shift
goto :parse_args

:args_done

REM 显示帮助信息
if "%SHOW_HELP%"=="true" (
    echo TRON 靓号地址生成器 Windows 构建脚本
    echo.
    echo 用法: %~nx0 [选项]
    echo.
    echo 选项:
    echo   -h, --help     显示帮助信息
    echo   -c, --clean    清理构建文件
    echo   -d, --debug    构建调试版本
    echo   -r, --release  构建发布版本
    echo   -t, --test     运行测试
    echo   -g, --gpu      启用GPU支持 ^(CUDA^)
    echo   -o, --opencl   启用OpenCL支持
    echo   --cpu-only     仅CPU版本
    echo.
    echo 示例:
    echo   %~nx0 --gpu --release    # 构建GPU加速的发布版本
    echo   %~nx0 --cpu-only --debug # 构建CPU版本的调试版本
    echo   %~nx0 --test             # 运行测试
    goto :end
)

echo %BLUE%[INFO]%NC% TRON 靓号地址生成器 Windows 构建脚本
echo %BLUE%[INFO]%NC% ======================================

REM 检查Visual Studio环境
echo %BLUE%[INFO]%NC% 检查构建环境...

REM 尝试找到Visual Studio
if not defined VCINSTALLDIR (
    echo %YELLOW%[WARNING]%NC% 未检测到Visual Studio环境，尝试自动设置...
    
    REM 查找Visual Studio 2019/2022
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else (
        echo %RED%[ERROR]%NC% 未找到Visual Studio，请手动设置环境变量
        goto :error
    )
)

REM 检查编译器
where cl >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 未找到MSVC编译器
    goto :error
)
echo %GREEN%[SUCCESS]%NC% 找到MSVC编译器

REM 检查CUDA（如果需要）
if "%ENABLE_CUDA%"=="true" (
    where nvcc >nul 2>&1
    if errorlevel 1 (
        echo %YELLOW%[WARNING]%NC% 未找到CUDA编译器，将禁用CUDA支持
        set "ENABLE_CUDA=false"
    ) else (
        echo %GREEN%[SUCCESS]%NC% 找到CUDA编译器
    )
)

REM 创建目录
if not exist "obj" mkdir obj
if not exist "bin" mkdir bin
if not exist "test_bin" mkdir test_bin

REM 清理构建文件
if "%CLEAN_BUILD%"=="true" (
    echo %BLUE%[INFO]%NC% 清理构建文件...
    if exist "obj\*" del /q obj\*
    if exist "bin\*" del /q bin\*
    if exist "test_bin\*" del /q test_bin\*
    echo %GREEN%[SUCCESS]%NC% 清理完成
)

REM 设置编译选项
set "CXXFLAGS=/std:c++17 /EHsc /Isrc"
set "LIBS=kernel32.lib user32.lib"

if "%BUILD_TYPE%"=="debug" (
    set "CXXFLAGS=%CXXFLAGS% /Od /Zi /MDd /DDEBUG"
    echo %BLUE%[INFO]%NC% 构建调试版本
) else (
    set "CXXFLAGS=%CXXFLAGS% /O2 /MD /DNDEBUG"
    echo %BLUE%[INFO]%NC% 构建发布版本
)

if "%ENABLE_CUDA%"=="true" (
    set "CXXFLAGS=%CXXFLAGS% /DUSE_CUDA"
    echo %BLUE%[INFO]%NC% 启用CUDA支持
) else if "%ENABLE_OPENCL%"=="true" (
    set "CXXFLAGS=%CXXFLAGS% /DUSE_OPENCL"
    set "LIBS=%LIBS% OpenCL.lib"
    echo %BLUE%[INFO]%NC% 启用OpenCL支持
) else (
    set "CXXFLAGS=%CXXFLAGS% /DCPU_ONLY"
    echo %BLUE%[INFO]%NC% 构建CPU版本
)

REM 编译源文件
echo %BLUE%[INFO]%NC% 编译源文件...

REM 编译C++源文件
set "CPP_FILES=src\main.cpp src\TronUtils.cpp src\Base58.cpp src\Keccak256.cpp src\SHA256.cpp src\Int256.cpp src\SimpleMatcher.cpp src\PatternUtils.cpp src\GPUEngineFactory.cpp src\TronVanitySearch.cpp"

if "%ENABLE_CUDA%"=="true" (
    set "CPP_FILES=%CPP_FILES% src\CUDAEngine.cpp src\GPUMemoryManager.cpp"
)

cl %CXXFLAGS% %CPP_FILES% %LIBS% /Fe:bin\TronVanity.exe

if errorlevel 1 (
    echo %RED%[ERROR]%NC% 编译失败
    goto :error
)

echo %GREEN%[SUCCESS]%NC% 编译完成

REM 编译CUDA内核（如果启用）
if "%ENABLE_CUDA%"=="true" (
    echo %BLUE%[INFO]%NC% 编译CUDA内核...
    nvcc -c src\TronKernel.cu -o obj\TronKernel.obj
    if errorlevel 1 (
        echo %YELLOW%[WARNING]%NC% CUDA内核编译失败，但主程序已完成
    ) else (
        echo %GREEN%[SUCCESS]%NC% CUDA内核编译完成
    )
)

REM 运行测试
if "%RUN_TESTS%"=="true" (
    echo %BLUE%[INFO]%NC% 运行测试...
    
    REM 编译测试程序
    echo %BLUE%[INFO]%NC% 编译测试程序...
    set "TEST_FILES=test\test_tron_vanity.cpp src\TronUtils.cpp src\Base58.cpp src\Keccak256.cpp src\SHA256.cpp src\Int256.cpp src\SimpleMatcher.cpp src\PatternUtils.cpp src\GPUEngineFactory.cpp"
    
    cl %CXXFLAGS% %TEST_FILES% %LIBS% /Fe:test_bin\test_tron_vanity.exe
    
    if errorlevel 1 (
        echo %RED%[ERROR]%NC% 测试程序编译失败
        goto :error
    )
    
    echo %GREEN%[SUCCESS]%NC% 测试程序编译完成
    
    REM 执行测试
    echo %BLUE%[INFO]%NC% 执行测试...
    test_bin\test_tron_vanity.exe
    
    if errorlevel 1 (
        echo %RED%[ERROR]%NC% 测试失败
        goto :error
    )
    
    echo %GREEN%[SUCCESS]%NC% 所有测试通过
)

REM 显示构建信息
echo.
echo %GREEN%[SUCCESS]%NC% 构建完成！
echo.
echo 可执行文件: bin\TronVanity.exe
echo.
echo 使用示例:
echo   bin\TronVanity.exe --help
echo   bin\TronVanity.exe -gpu -stop TMyPrefix
echo   bin\TronVanity.exe -cpu -c -o results.txt TTest
echo.

goto :end

:error
echo %RED%[ERROR]%NC% 构建失败
exit /b 1

:end
endlocal
