#pragma once

#include <vector>
#include <string>
#include <memory>
#include <atomic>
#include <functional>

#ifdef USE_CUDA
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#endif

#ifdef USE_OPENCL
#include <CL/cl.h>
#endif

// GPU设备信息
struct GPUDeviceInfo {
    int deviceId;
    std::string name;
    size_t globalMemory;
    size_t sharedMemory;
    int multiProcessors;
    int maxThreadsPerBlock;
    int maxThreadsPerMultiProcessor;
    int warpSize;
    int computeCapabilityMajor;
    int computeCapabilityMinor;
    bool isAvailable;
};

// GPU内核配置
struct GPUKernelConfig {
    int gridSizeX = 256;
    int gridSizeY = 128;
    int blockSizeX = 256;
    int blockSizeY = 1;
    int sharedMemorySize = 0;
    int maxRegistersPerThread = 32;
};

// GPU搜索结果
struct GPUSearchResult {
    uint8_t privateKey[32];
    uint8_t publicKey[64];
    char address[35];  // Base58编码的TRON地址
    uint64_t iteration;
    bool found;
};

// GPU内存管理器
class GPUMemoryManager {
public:
    GPUMemoryManager(int deviceId);
    ~GPUMemoryManager();
    
    // 内存分配
    void* allocateDevice(size_t size);
    void* allocateHost(size_t size);
    void* allocateHostPinned(size_t size);
    
    // 内存释放
    void freeDevice(void* ptr);
    void freeHost(void* ptr);
    void freeHostPinned(void* ptr);
    
    // 内存拷贝
    bool copyHostToDevice(void* dst, const void* src, size_t size);
    bool copyDeviceToHost(void* dst, const void* src, size_t size);
    bool copyDeviceToDevice(void* dst, const void* src, size_t size);
    
    // 异步内存拷贝
    bool copyHostToDeviceAsync(void* dst, const void* src, size_t size, void* stream);
    bool copyDeviceToHostAsync(void* dst, const void* src, size_t size, void* stream);
    
    // 内存信息
    size_t getAvailableMemory() const;
    size_t getTotalMemory() const;

private:
    int m_deviceId;
    size_t m_totalMemory;
    size_t m_availableMemory;
};

// GPU引擎基类
class GPUEngine {
public:
    GPUEngine();
    virtual ~GPUEngine();
    
    // 初始化和清理
    virtual bool initialize() = 0;
    virtual void cleanup() = 0;
    
    // 设备管理
    virtual std::vector<GPUDeviceInfo> getDeviceList() = 0;
    virtual bool selectDevice(int deviceId) = 0;
    virtual GPUDeviceInfo getCurrentDevice() = 0;
    
    // 内核配置
    virtual bool setKernelConfig(const GPUKernelConfig& config) = 0;
    virtual GPUKernelConfig getKernelConfig() const = 0;
    virtual GPUKernelConfig getOptimalConfig() = 0;
    
    // 搜索控制
    virtual bool startSearch(const std::vector<std::string>& patterns) = 0;
    virtual void stopSearch() = 0;
    virtual bool isSearching() const = 0;
    
    // 结果获取
    virtual std::vector<GPUSearchResult> getResults() = 0;
    virtual uint64_t getIterationCount() const = 0;
    virtual double getHashRate() const = 0;
    
    // 回调设置
    void setResultCallback(std::function<void(const GPUSearchResult&)> callback);
    void setProgressCallback(std::function<void(uint64_t, double)> callback);

protected:
    GPUKernelConfig m_config;
    std::atomic<bool> m_running{false};
    std::atomic<uint64_t> m_iterations{0};
    std::atomic<double> m_hashRate{0.0};
    
    std::function<void(const GPUSearchResult&)> m_resultCallback;
    std::function<void(uint64_t, double)> m_progressCallback;
    
    virtual void updateStats() = 0;
};

#ifdef USE_CUDA
// CUDA GPU引擎
class CUDAEngine : public GPUEngine {
public:
    CUDAEngine();
    ~CUDAEngine() override;
    
    // GPUEngine接口实现
    bool initialize() override;
    void cleanup() override;
    
    std::vector<GPUDeviceInfo> getDeviceList() override;
    bool selectDevice(int deviceId) override;
    GPUDeviceInfo getCurrentDevice() override;
    
    bool setKernelConfig(const GPUKernelConfig& config) override;
    GPUKernelConfig getOptimalConfig() override;
    
    bool startSearch(const std::vector<std::string>& patterns) override;
    void stopSearch() override;
    bool isSearching() const override;
    
    std::vector<GPUSearchResult> getResults() override;
    uint64_t getIterationCount() const override;
    double getHashRate() const override;

private:
    int m_deviceId;
    cudaDeviceProp m_deviceProp;
    cudaStream_t m_stream;
    
    // GPU内存
    uint8_t* d_privateKeys;
    uint8_t* d_publicKeys;
    char* d_addresses;
    GPUSearchResult* d_results;
    uint32_t* d_patterns;
    bool* d_found;
    
    // 主机内存
    GPUSearchResult* h_results;
    
    std::unique_ptr<GPUMemoryManager> m_memoryManager;
    
    void updateStats() override;
    bool allocateMemory();
    void freeMemory();
    bool copyPatternsToDevice(const std::vector<std::string>& patterns);
    void launchKernel();
    bool checkResults();
};
#endif

#ifdef USE_OPENCL
// OpenCL GPU引擎
class OpenCLEngine : public GPUEngine {
public:
    OpenCLEngine();
    ~OpenCLEngine() override;
    
    // GPUEngine接口实现
    bool initialize() override;
    void cleanup() override;
    
    std::vector<GPUDeviceInfo> getDeviceList() override;
    bool selectDevice(int deviceId) override;
    GPUDeviceInfo getCurrentDevice() override;
    
    bool setKernelConfig(const GPUKernelConfig& config) override;
    GPUKernelConfig getOptimalConfig() override;
    
    bool startSearch(const std::vector<std::string>& patterns) override;
    void stopSearch() override;
    bool isSearching() const override;
    
    std::vector<GPUSearchResult> getResults() override;
    uint64_t getIterationCount() const override;
    double getHashRate() const override;

private:
    cl_platform_id m_platform;
    cl_device_id m_device;
    cl_context m_context;
    cl_command_queue m_queue;
    cl_program m_program;
    cl_kernel m_kernel;
    
    // OpenCL内存对象
    cl_mem m_privateKeys;
    cl_mem m_publicKeys;
    cl_mem m_addresses;
    cl_mem m_results;
    cl_mem m_patterns;
    cl_mem m_found;
    
    GPUSearchResult* m_hostResults;
    
    void updateStats() override;
    bool buildProgram();
    bool allocateMemory();
    void freeMemory();
    bool copyPatternsToDevice(const std::vector<std::string>& patterns);
    void launchKernel();
    bool checkResults();
};
#endif

// GPU引擎工厂
class GPUEngineFactory {
public:
    enum EngineType {
        AUTO_DETECT,
        CUDA_ENGINE,
        OPENCL_ENGINE
    };
    
    static std::unique_ptr<GPUEngine> createEngine(EngineType type = AUTO_DETECT);
    static std::vector<EngineType> getAvailableEngines();
    static std::string getEngineTypeName(EngineType type);
    static bool isCUDAAvailable();
    static bool isOpenCLAvailable();
};

// GPU工具函数
namespace GPUUtils {
    // 获取最佳网格大小
    GPUKernelConfig calculateOptimalConfig(const GPUDeviceInfo& device, size_t workSize);
    
    // 性能基准测试
    double benchmarkDevice(GPUEngine* engine, int seconds = 10);
    
    // 内存使用估算
    size_t estimateMemoryUsage(const GPUKernelConfig& config);
    
    // 错误处理
    std::string getLastError();
    void clearLastError();
}
