#pragma once

#include <vector>
#include <cstdint>
#include <string>

// Keccak256哈希类 (TRON使用的哈希算法)
class Keccak256 {
public:
    static const int HASH_SIZE = 32;
    static const int BLOCK_SIZE = 136; // 1088 bits / 8
    
    Keccak256();
    ~Keccak256();
    
    // 一次性哈希
    static std::vector<uint8_t> hash(const std::vector<uint8_t>& data);
    static std::vector<uint8_t> hash(const uint8_t* data, size_t length);
    static std::string hashHex(const std::vector<uint8_t>& data);
    
    // 流式哈希
    void reset();
    void update(const std::vector<uint8_t>& data);
    void update(const uint8_t* data, size_t length);
    std::vector<uint8_t> finalize();
    std::string finalizeHex();

private:
    uint64_t m_state[25];  // Keccak状态 (1600 bits)
    uint8_t m_buffer[BLOCK_SIZE];
    size_t m_bufferSize;
    bool m_finalized;
    
    void processBlock();
    void keccakF();
    void theta();
    void rho();
    void pi();
    void chi();
    void iota(int round);
    
    static const uint64_t ROUND_CONSTANTS[24];
    static const int RHO_OFFSETS[24];
};

// SHA256哈希类 (用于Base58Check校验)
class SHA256 {
public:
    static const int HASH_SIZE = 32;
    static const int BLOCK_SIZE = 64;
    
    SHA256();
    ~SHA256();
    
    // 一次性哈希
    static std::vector<uint8_t> hash(const std::vector<uint8_t>& data);
    static std::vector<uint8_t> hash(const uint8_t* data, size_t length);
    static std::string hashHex(const std::vector<uint8_t>& data);
    
    // 双重SHA256 (用于Base58Check)
    static std::vector<uint8_t> doubleHash(const std::vector<uint8_t>& data);
    
    // 流式哈希
    void reset();
    void update(const std::vector<uint8_t>& data);
    void update(const uint8_t* data, size_t length);
    std::vector<uint8_t> finalize();
    std::string finalizeHex();

private:
    uint32_t m_state[8];
    uint8_t m_buffer[BLOCK_SIZE];
    size_t m_bufferSize;
    uint64_t m_totalLength;
    bool m_finalized;
    
    void processBlock();
    void processBlock(const uint8_t* block);
    
    static const uint32_t K[64];
    static uint32_t rotr(uint32_t x, int n);
    static uint32_t ch(uint32_t x, uint32_t y, uint32_t z);
    static uint32_t maj(uint32_t x, uint32_t y, uint32_t z);
    static uint32_t sigma0(uint32_t x);
    static uint32_t sigma1(uint32_t x);
    static uint32_t gamma0(uint32_t x);
    static uint32_t gamma1(uint32_t x);
};

// Base58编码类
class Base58 {
public:
    // Base58字符表 (Bitcoin/TRON使用)
    static const char ALPHABET[58];
    
    // 基本编码/解码
    static std::string encode(const std::vector<uint8_t>& data);
    static std::vector<uint8_t> decode(const std::string& encoded);
    
    // Base58Check编码/解码 (带校验和)
    static std::string encodeCheck(const std::vector<uint8_t>& data);
    static std::vector<uint8_t> decodeCheck(const std::string& encoded);
    
    // 验证Base58Check格式
    static bool isValidCheck(const std::string& encoded);

private:
    static std::vector<uint8_t> calculateChecksum(const std::vector<uint8_t>& data);
};

// RIPEMD160哈希类 (备用，某些场景可能需要)
class RIPEMD160 {
public:
    static const int HASH_SIZE = 20;
    static const int BLOCK_SIZE = 64;
    
    RIPEMD160();
    ~RIPEMD160();
    
    // 一次性哈希
    static std::vector<uint8_t> hash(const std::vector<uint8_t>& data);
    static std::vector<uint8_t> hash(const uint8_t* data, size_t length);
    static std::string hashHex(const std::vector<uint8_t>& data);
    
    // 流式哈希
    void reset();
    void update(const std::vector<uint8_t>& data);
    void update(const uint8_t* data, size_t length);
    std::vector<uint8_t> finalize();
    std::string finalizeHex();

private:
    uint32_t m_state[5];
    uint8_t m_buffer[BLOCK_SIZE];
    size_t m_bufferSize;
    uint64_t m_totalLength;
    bool m_finalized;
    
    void processBlock();
    void processBlock(const uint8_t* block);
    
    static uint32_t f(int j, uint32_t x, uint32_t y, uint32_t z);
    static uint32_t rotl(uint32_t x, int n);
};

// 哈希工具函数
namespace HashUtils {
    // 十六进制字符串转换
    std::string bytesToHex(const std::vector<uint8_t>& bytes);
    std::vector<uint8_t> hexToBytes(const std::string& hex);
    
    // 字符串哈希
    std::vector<uint8_t> keccak256String(const std::string& str);
    std::vector<uint8_t> sha256String(const std::string& str);
    
    // 验证哈希结果
    bool verifyKeccak256(const std::vector<uint8_t>& data, const std::vector<uint8_t>& expectedHash);
    bool verifySHA256(const std::vector<uint8_t>& data, const std::vector<uint8_t>& expectedHash);
    
    // 性能测试
    double benchmarkKeccak256(int iterations = 10000);
    double benchmarkSHA256(int iterations = 10000);
}
