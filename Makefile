# TRON Vanity Address Generator Makefile

# 编译器设置
CXX = g++
NVCC = nvcc

# 编译标志
CXXFLAGS = -std=c++17 -O3 -march=native -Wall -Wextra
NVCCFLAGS = -std=c++17 -O3 -arch=sm_60 --ptxas-options=-v

# 包含目录
INCLUDES = -Isrc

# 库链接
LIBS = -lpthread -lm

# CUDA设置
CUDA_PATH ?= /usr/local/cuda
CUDA_LIBS = -L$(CUDA_PATH)/lib64 -lcuda -lcudart
CUDA_INCLUDES = -I$(CUDA_PATH)/include

# OpenCL设置
OPENCL_LIBS = -lOpenCL

# 源文件目录
SRCDIR = src
OBJDIR = obj
BINDIR = bin

# 源文件
CPP_SOURCES = $(wildcard $(SRCDIR)/*.cpp)
CU_SOURCES = $(wildcard $(SRCDIR)/*.cu)

# 目标文件
CPP_OBJECTS = $(CPP_SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/%.o)
CU_OBJECTS = $(CU_SOURCES:$(SRCDIR)/%.cu=$(OBJDIR)/%.o)

# 可执行文件
TARGET = $(BINDIR)/TronVanity

# 默认目标
.PHONY: all clean cpu gpu cuda opencl install

all: cpu

# CPU版本 (不包含GPU支持)
cpu: CXXFLAGS += -DCPU_ONLY
cpu: $(TARGET)

# GPU版本 (自动检测CUDA/OpenCL)
gpu: cuda

# CUDA版本
cuda: CXXFLAGS += -DUSE_CUDA
cuda: INCLUDES += $(CUDA_INCLUDES)
cuda: LIBS += $(CUDA_LIBS)
cuda: $(TARGET)

# OpenCL版本
opencl: CXXFLAGS += -DUSE_OPENCL
opencl: LIBS += $(OPENCL_LIBS)
opencl: $(TARGET)

# 创建目录
$(OBJDIR):
	mkdir -p $(OBJDIR)

$(BINDIR):
	mkdir -p $(BINDIR)

# 编译C++源文件
$(OBJDIR)/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 编译CUDA源文件
$(OBJDIR)/%.o: $(SRCDIR)/%.cu | $(OBJDIR)
	$(NVCC) $(NVCCFLAGS) $(INCLUDES) $(CUDA_INCLUDES) -c $< -o $@

# 链接可执行文件
$(TARGET): $(CPP_OBJECTS) $(CU_OBJECTS) | $(BINDIR)
	$(CXX) $(CPP_OBJECTS) $(CU_OBJECTS) $(LIBS) -o $@

# 清理
clean:
	rm -rf $(OBJDIR) $(BINDIR)

# 安装
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/

# 卸载
uninstall:
	rm -f /usr/local/bin/TronVanity

# 测试
test: $(TARGET)
	./$(TARGET) --help
	./$(TARGET) -l

# 性能测试
benchmark: $(TARGET)
	./$(TARGET) -gpu --verbose TTest

# 调试版本
debug: CXXFLAGS += -g -DDEBUG -O0
debug: NVCCFLAGS += -g -G -O0
debug: $(TARGET)

# 发布版本
release: CXXFLAGS += -DNDEBUG -flto
release: NVCCFLAGS += -DNDEBUG
release: $(TARGET)
	strip $(TARGET)

# 依赖关系
$(OBJDIR)/main.o: $(SRCDIR)/main.cpp $(SRCDIR)/TronVanity.h
$(OBJDIR)/TronUtils.o: $(SRCDIR)/TronUtils.cpp $(SRCDIR)/TronVanity.h $(SRCDIR)/Hash.h $(SRCDIR)/Secp256k1.h
$(OBJDIR)/Base58.o: $(SRCDIR)/Base58.cpp $(SRCDIR)/Hash.h
$(OBJDIR)/Keccak256.o: $(SRCDIR)/Keccak256.cpp $(SRCDIR)/Hash.h
$(OBJDIR)/SHA256.o: $(SRCDIR)/SHA256.cpp $(SRCDIR)/Hash.h
$(OBJDIR)/Int256.o: $(SRCDIR)/Int256.cpp $(SRCDIR)/Secp256k1.h

# 帮助信息
help:
	@echo "TRON Vanity Address Generator Makefile"
	@echo ""
	@echo "目标:"
	@echo "  all        - 编译默认版本 (CPU)"
	@echo "  cpu        - 编译CPU版本"
	@echo "  gpu        - 编译GPU版本 (CUDA)"
	@echo "  cuda       - 编译CUDA版本"
	@echo "  opencl     - 编译OpenCL版本"
	@echo "  debug      - 编译调试版本"
	@echo "  release    - 编译发布版本"
	@echo "  clean      - 清理编译文件"
	@echo "  install    - 安装到系统"
	@echo "  uninstall  - 从系统卸载"
	@echo "  test       - 运行基本测试"
	@echo "  benchmark  - 运行性能测试"
	@echo "  help       - 显示此帮助信息"
	@echo ""
	@echo "变量:"
	@echo "  CXX        - C++编译器 (默认: g++)"
	@echo "  NVCC       - CUDA编译器 (默认: nvcc)"
	@echo "  CUDA_PATH  - CUDA安装路径 (默认: /usr/local/cuda)"
	@echo "  CCAP       - CUDA计算能力 (默认: 6.0)"
	@echo ""
	@echo "示例:"
	@echo "  make cuda CCAP=7.5"
	@echo "  make opencl CXX=clang++"
	@echo "  make debug"

# 检查依赖
check-deps:
	@echo "检查编译依赖..."
	@which $(CXX) > /dev/null || (echo "错误: 未找到C++编译器 $(CXX)" && exit 1)
	@echo "C++编译器: $(CXX) ✓"
	@if [ "$(findstring cuda,$(MAKECMDGOALS))" != "" ]; then \
		which $(NVCC) > /dev/null || (echo "错误: 未找到CUDA编译器 $(NVCC)" && exit 1); \
		echo "CUDA编译器: $(NVCC) ✓"; \
		test -d $(CUDA_PATH) || (echo "错误: CUDA路径不存在 $(CUDA_PATH)" && exit 1); \
		echo "CUDA路径: $(CUDA_PATH) ✓"; \
	fi
	@echo "依赖检查完成"

# 在编译前检查依赖
$(TARGET): check-deps

.PHONY: help check-deps
