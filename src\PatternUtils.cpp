#include "PatternMatcher.h"
#include "TronVanity.h"
#include <algorithm>
#include <cmath>
#include <random>
#include <set>

namespace PatternUtils {

// Base58字符集
const std::string BASE58_CHARS = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";

bool isValidPattern(const std::string& pattern, PatternType type) {
    if (pattern.empty()) {
        return false;
    }
    
    switch (type) {
        case PatternType::PREFIX:
        case PatternType::SUFFIX:
        case PatternType::CONTAINS:
            return isValidTronPattern(pattern);
            
        case PatternType::WILDCARD:
            // 检查通配符模式
            for (char c : pattern) {
                if (c != '?' && c != '*' && BASE58_CHARS.find(c) == std::string::npos) {
                    return false;
                }
            }
            return true;
            
        case PatternType::REGEX:
            // 尝试编译正则表达式
            try {
                std::regex regex(pattern);
                return true;
            } catch (const std::regex_error&) {
                return false;
            }
            
        default:
            return false;
    }
}

bool isValidTronPattern(const std::string& pattern) {
    if (pattern.empty()) {
        return false;
    }
    
    // 检查长度（TRON地址长度为34）
    if (pattern.length() > 34) {
        return false;
    }
    
    // 检查字符是否都在Base58字符集中
    for (char c : pattern) {
        if (BASE58_CHARS.find(c) == std::string::npos) {
            return false;
        }
    }
    
    // TRON地址必须以'T'开头
    if (pattern[0] != 'T') {
        return false;
    }
    
    return true;
}

double calculatePatternDifficulty(const std::string& pattern, PatternType type) {
    if (pattern.empty()) {
        return 1.0;
    }
    
    const double base58Size = 58.0;
    
    switch (type) {
        case PatternType::PREFIX:
            return std::pow(base58Size, pattern.length());
            
        case PatternType::SUFFIX:
            return std::pow(base58Size, pattern.length());
            
        case PatternType::CONTAINS: {
            // 包含匹配的难度取决于模式长度和位置
            double baseDifficulty = std::pow(base58Size, pattern.length());
            // 考虑可能的位置数量
            int possiblePositions = 34 - static_cast<int>(pattern.length()) + 1;
            return baseDifficulty / possiblePositions;
        }
        
        case PatternType::WILDCARD: {
            double difficulty = 1.0;
            int fixedChars = 0;
            
            for (char c : pattern) {
                if (c == '?') {
                    difficulty *= base58Size;
                } else if (c == '*') {
                    // 通配符*降低难度
                    difficulty *= 10.0;
                } else {
                    difficulty *= base58Size;
                    fixedChars++;
                }
            }
            
            // 固定字符越多，难度越高
            if (fixedChars > 0) {
                difficulty *= std::pow(1.5, fixedChars);
            }
            
            return difficulty;
        }
        
        case PatternType::REGEX: {
            // 正则表达式难度估算（简化）
            double complexity = 1.0;
            
            // 基于模式长度估算
            complexity *= std::pow(base58Size, pattern.length() / 3.0);
            
            // 检查特殊字符
            if (pattern.find('[') != std::string::npos) complexity *= 0.5;  // 字符类降低难度
            if (pattern.find('+') != std::string::npos) complexity *= 2.0;  // 重复增加难度
            if (pattern.find('*') != std::string::npos) complexity *= 0.3;  // 通配符降低难度
            if (pattern.find('?') != std::string::npos) complexity *= 0.7;  // 可选字符降低难度
            
            return complexity;
        }
        
        default:
            return 1.0;
    }
}

uint64_t estimateSearchTime(double difficulty, double hashRate) {
    if (hashRate <= 0) {
        return UINT64_MAX;
    }
    
    // 50%概率找到匹配所需的时间（秒）
    double expectedAttempts = difficulty * 0.693;  // ln(2)
    return static_cast<uint64_t>(expectedAttempts / hashRate);
}

std::string normalizePattern(const std::string& pattern, bool caseInsensitive) {
    std::string normalized = pattern;
    
    if (caseInsensitive) {
        std::transform(normalized.begin(), normalized.end(), normalized.begin(), ::tolower);
    }
    
    // 移除前导和尾随空格
    normalized.erase(0, normalized.find_first_not_of(" \t\n\r"));
    normalized.erase(normalized.find_last_not_of(" \t\n\r") + 1);
    
    return normalized;
}

PatternType detectPatternType(const std::string& pattern) {
    if (pattern.empty()) {
        return PatternType::PREFIX;
    }
    
    // 检查是否包含通配符
    if (pattern.find('*') != std::string::npos || pattern.find('?') != std::string::npos) {
        return PatternType::WILDCARD;
    }
    
    // 检查是否包含正则表达式特殊字符
    const std::string regexChars = "[](){}^$+|\\";
    for (char c : regexChars) {
        if (pattern.find(c) != std::string::npos) {
            return PatternType::REGEX;
        }
    }
    
    // 检查是否以*开头（后缀匹配）
    if (pattern[0] == '*') {
        return PatternType::SUFFIX;
    }
    
    // 检查是否以*结尾（前缀匹配）
    if (pattern.back() == '*') {
        return PatternType::PREFIX;
    }
    
    // 默认为前缀匹配
    return PatternType::PREFIX;
}

std::vector<std::string> generateSimilarPatterns(const std::string& pattern, int count) {
    std::vector<std::string> patterns;
    std::set<std::string> uniquePatterns;
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> charDis(0, BASE58_CHARS.length() - 1);
    std::uniform_int_distribution<> posDis(0, pattern.length() - 1);
    
    // 添加原始模式
    uniquePatterns.insert(pattern);
    patterns.push_back(pattern);
    
    while (patterns.size() < count && patterns.size() < 1000) {  // 防止无限循环
        std::string newPattern = pattern;
        
        // 随机修改策略
        int strategy = gen() % 4;
        
        switch (strategy) {
            case 0: {
                // 替换一个字符
                if (!newPattern.empty()) {
                    int pos = posDis(gen) % newPattern.length();
                    newPattern[pos] = BASE58_CHARS[charDis(gen)];
                }
                break;
            }
            
            case 1: {
                // 添加一个字符
                if (newPattern.length() < 10) {
                    int pos = gen() % (newPattern.length() + 1);
                    newPattern.insert(pos, 1, BASE58_CHARS[charDis(gen)]);
                }
                break;
            }
            
            case 2: {
                // 删除一个字符
                if (newPattern.length() > 2) {
                    int pos = posDis(gen) % newPattern.length();
                    newPattern.erase(pos, 1);
                }
                break;
            }
            
            case 3: {
                // 添加通配符
                if (newPattern.length() < 10) {
                    int pos = gen() % (newPattern.length() + 1);
                    char wildcard = (gen() % 2 == 0) ? '?' : '*';
                    newPattern.insert(pos, 1, wildcard);
                }
                break;
            }
        }
        
        // 确保模式有效且唯一
        if (isValidPattern(newPattern, PatternType::WILDCARD) && 
            uniquePatterns.find(newPattern) == uniquePatterns.end()) {
            uniquePatterns.insert(newPattern);
            patterns.push_back(newPattern);
        }
    }
    
    return patterns;
}

double calculateSuccessProbability(uint64_t attempts, double difficulty) {
    if (difficulty <= 0) {
        return 1.0;
    }
    
    // 使用泊松分布计算成功概率
    double lambda = static_cast<double>(attempts) / difficulty;
    
    // P(至少找到一个) = 1 - P(找到零个) = 1 - e^(-lambda)
    return 1.0 - std::exp(-lambda);
}

} // namespace PatternUtils

// MatcherFactory实现
std::unique_ptr<PatternMatcher> MatcherFactory::createMatcher(MatcherType type) {
    switch (type) {
        case SIMPLE_MATCHER:
            return std::make_unique<SimpleMatcher>();
            
        case OPTIMIZED_MATCHER:
            // return std::make_unique<OptimizedMatcher>();
            // 暂时回退到简单匹配器
            return std::make_unique<SimpleMatcher>();
            
        case GPU_MATCHER:
            // return std::make_unique<GPUMatcher>();
            // 暂时回退到简单匹配器
            return std::make_unique<SimpleMatcher>();
            
        default:
            return std::make_unique<SimpleMatcher>();
    }
}

MatcherFactory::MatcherType MatcherFactory::getBestMatcher(int patternCount, bool useGPU) {
    if (useGPU && patternCount <= 32) {
        return GPU_MATCHER;
    } else if (patternCount > 100) {
        return OPTIMIZED_MATCHER;
    } else {
        return SIMPLE_MATCHER;
    }
}
