#pragma once

#include <vector>
#include <cstdint>

// secp256k1椭圆曲线参数
namespace Secp256k1 {
    // 曲线参数 y² = x³ + 7 (mod p)
    extern const uint8_t CURVE_P[32];      // 素数模数
    extern const uint8_t CURVE_N[32];      // 曲线阶数
    extern const uint8_t CURVE_G[64];      // 基点G (未压缩格式)
    extern const uint8_t CURVE_B[32];      // 曲线参数b = 7
    
    // 预计算表大小
    static const int PRECOMPUTE_TABLE_SIZE = 256;
    static const int WINDOW_SIZE = 8;
}

// 大整数运算类 (256位)
class Int256 {
public:
    uint64_t data[4];  // 4个64位整数，小端序
    
    Int256();
    Int256(uint64_t value);
    Int256(const uint8_t* bytes);
    Int256(const std::vector<uint8_t>& bytes);
    
    // 基本运算
    Int256 operator+(const Int256& other) const;
    Int256 operator-(const Int256& other) const;
    Int256 operator*(const Int256& other) const;
    Int256 operator%(const Int256& other) const;
    
    Int256& operator+=(const Int256& other);
    Int256& operator-=(const Int256& other);
    Int256& operator*=(const Int256& other);
    Int256& operator%=(const Int256& other);
    
    // 比较运算
    bool operator==(const Int256& other) const;
    bool operator!=(const Int256& other) const;
    bool operator<(const Int256& other) const;
    bool operator>(const Int256& other) const;
    bool operator<=(const Int256& other) const;
    bool operator>=(const Int256& other) const;
    
    // 位运算
    Int256 operator<<(int shift) const;
    Int256 operator>>(int shift) const;
    Int256& operator<<=(int shift);
    Int256& operator>>=(int shift);
    
    // 工具方法
    bool isZero() const;
    bool isOdd() const;
    int getBitLength() const;
    void setZero();
    void setOne();
    
    // 转换方法
    std::vector<uint8_t> toBytes() const;
    std::string toHex() const;
    void fromBytes(const uint8_t* bytes);
    void fromHex(const std::string& hex);
    
    // 模运算
    Int256 modAdd(const Int256& other, const Int256& mod) const;
    Int256 modSub(const Int256& other, const Int256& mod) const;
    Int256 modMul(const Int256& other, const Int256& mod) const;
    Int256 modInv(const Int256& mod) const;
    Int256 modPow(const Int256& exp, const Int256& mod) const;
};

// 椭圆曲线点类
class ECPoint {
public:
    Int256 x, y;
    bool isInfinity;
    
    ECPoint();
    ECPoint(const Int256& x, const Int256& y);
    
    // 点运算
    ECPoint operator+(const ECPoint& other) const;
    ECPoint operator-(const ECPoint& other) const;
    ECPoint operator*(const Int256& scalar) const;
    
    ECPoint& operator+=(const ECPoint& other);
    ECPoint& operator-=(const ECPoint& other);
    ECPoint& operator*=(const Int256& scalar);
    
    // 比较
    bool operator==(const ECPoint& other) const;
    bool operator!=(const ECPoint& other) const;
    
    // 工具方法
    void setInfinity();
    bool isValid() const;
    ECPoint negate() const;
    ECPoint double_point() const;
    
    // 转换方法
    std::vector<uint8_t> toBytes(bool compressed = true) const;
    void fromBytes(const std::vector<uint8_t>& bytes);
    std::string toHex(bool compressed = true) const;
    void fromHex(const std::string& hex);
};

// secp256k1椭圆曲线类
class Secp256k1Curve {
public:
    Secp256k1Curve();
    ~Secp256k1Curve();
    
    // 基本运算
    ECPoint getGenerator() const;
    ECPoint multiply(const ECPoint& point, const Int256& scalar) const;
    ECPoint add(const ECPoint& p1, const ECPoint& p2) const;
    ECPoint double_point(const ECPoint& point) const;
    
    // 快速标量乘法 (使用预计算表)
    ECPoint multiplyFast(const Int256& scalar) const;
    
    // 验证点是否在曲线上
    bool isOnCurve(const ECPoint& point) const;
    
    // 压缩/解压缩点
    ECPoint decompress(const std::vector<uint8_t>& compressed) const;
    std::vector<uint8_t> compress(const ECPoint& point) const;
    
    // 从私钥生成公钥
    ECPoint privateKeyToPublicKey(const Int256& privateKey) const;
    
    // 获取曲线参数
    Int256 getP() const { return m_p; }
    Int256 getN() const { return m_n; }
    ECPoint getG() const { return m_g; }

private:
    Int256 m_p;  // 素数模数
    Int256 m_n;  // 曲线阶数
    ECPoint m_g; // 基点
    
    // 预计算表 (用于快速标量乘法)
    std::vector<ECPoint> m_precomputeTable;
    
    void initializePrecomputeTable();
    ECPoint montgomeryLadder(const ECPoint& point, const Int256& scalar) const;
    ECPoint windowMethod(const Int256& scalar) const;
};

// 模运算工具函数
namespace ModularArithmetic {
    // 模加法
    Int256 addMod(const Int256& a, const Int256& b, const Int256& mod);
    
    // 模减法
    Int256 subMod(const Int256& a, const Int256& b, const Int256& mod);
    
    // 模乘法 (Montgomery算法)
    Int256 mulMod(const Int256& a, const Int256& b, const Int256& mod);
    
    // 模逆 (扩展欧几里得算法)
    Int256 invMod(const Int256& a, const Int256& mod);
    
    // 模幂 (快速幂算法)
    Int256 powMod(const Int256& base, const Int256& exp, const Int256& mod);
    
    // 平方根模素数 (Tonelli-Shanks算法)
    Int256 sqrtMod(const Int256& a, const Int256& p);
    
    // 判断是否为二次剩余
    bool isQuadraticResidue(const Int256& a, const Int256& p);
}
