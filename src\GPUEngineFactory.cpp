#include "GPUEngine.h"
#include <iostream>

std::unique_ptr<GPUEngine> GPUEngineFactory::createEngine(EngineType type) {
    if (type == AUTO_DETECT) {
        // 自动检测最佳引擎
        if (isCUDAAvailable()) {
            type = CUDA_ENGINE;
        } else if (isOpenCLAvailable()) {
            type = OPENCL_ENGINE;
        } else {
            std::cerr << "未找到可用的GPU引擎" << std::endl;
            return nullptr;
        }
    }
    
    switch (type) {
#ifdef USE_CUDA
        case CUDA_ENGINE:
            return std::make_unique<CUDAEngine>();
#endif
            
#ifdef USE_OPENCL
        case OPENCL_ENGINE:
            return std::make_unique<OpenCLEngine>();
#endif
            
        default:
            std::cerr << "不支持的GPU引擎类型" << std::endl;
            return nullptr;
    }
}

std::vector<GPUEngineFactory::EngineType> GPUEngineFactory::getAvailableEngines() {
    std::vector<EngineType> engines;
    
#ifdef USE_CUDA
    if (isCUDAAvailable()) {
        engines.push_back(CUDA_ENGINE);
    }
#endif
    
#ifdef USE_OPENCL
    if (isOpenCLAvailable()) {
        engines.push_back(OPENCL_ENGINE);
    }
#endif
    
    return engines;
}

std::string GPUEngineFactory::getEngineTypeName(EngineType type) {
    switch (type) {
        case AUTO_DETECT: return "自动检测";
        case CUDA_ENGINE: return "CUDA";
        case OPENCL_ENGINE: return "OpenCL";
        default: return "未知";
    }
}

bool GPUEngineFactory::isCUDAAvailable() {
#ifdef USE_CUDA
    int deviceCount;
    cudaError_t error = cudaGetDeviceCount(&deviceCount);
    return (error == cudaSuccess && deviceCount > 0);
#else
    return false;
#endif
}

bool GPUEngineFactory::isOpenCLAvailable() {
#ifdef USE_OPENCL
    cl_uint platformCount;
    cl_int error = clGetPlatformIDs(0, nullptr, &platformCount);
    return (error == CL_SUCCESS && platformCount > 0);
#else
    return false;
#endif
}

// GPU工具函数实现
namespace GPUUtils {

GPUKernelConfig calculateOptimalConfig(const GPUDeviceInfo& device, size_t workSize) {
    GPUKernelConfig config;
    
    // 基于设备属性计算最优配置
    config.blockSizeX = std::min(256, device.maxThreadsPerBlock);
    config.blockSizeY = 1;
    
    // 计算网格大小
    size_t totalThreads = config.blockSizeX * config.blockSizeY;
    config.gridSizeX = (workSize + totalThreads - 1) / totalThreads;
    config.gridSizeY = 1;
    
    // 限制网格大小
    config.gridSizeX = std::min(config.gridSizeX, device.multiProcessors * 8);
    
    // 共享内存配置
    config.sharedMemorySize = device.sharedMemory / 2;
    config.maxRegistersPerThread = 32;
    
    return config;
}

double benchmarkDevice(GPUEngine* engine, int seconds) {
    if (!engine || !engine->initialize()) {
        return 0.0;
    }
    
    // 设置测试模式
    std::vector<std::string> testPatterns = {"TTest"};
    
    auto startTime = std::chrono::steady_clock::now();
    auto endTime = startTime + std::chrono::seconds(seconds);
    
    uint64_t initialIterations = engine->getIterationCount();
    
    // 启动搜索
    engine->startSearch(testPatterns);
    
    // 等待指定时间
    std::this_thread::sleep_until(endTime);
    
    // 停止搜索
    engine->stopSearch();
    
    uint64_t finalIterations = engine->getIterationCount();
    uint64_t totalIterations = finalIterations - initialIterations;
    
    return static_cast<double>(totalIterations) / seconds;
}

size_t estimateMemoryUsage(const GPUKernelConfig& config) {
    size_t totalThreads = config.gridSizeX * config.gridSizeY * 
                         config.blockSizeX * config.blockSizeY;
    
    // 估算每个线程的内存使用量
    size_t memoryPerThread = 
        32 +    // 私钥
        64 +    // 公钥
        35 +    // 地址
        8 +     // 迭代计数
        16;     // 其他数据
    
    size_t totalMemory = totalThreads * memoryPerThread;
    
    // 添加模式数据和其他开销
    totalMemory += 1024 * 1024;  // 1MB开销
    
    return totalMemory;
}

std::string getLastError() {
    static std::string lastError;
    
#ifdef USE_CUDA
    cudaError_t cudaError = cudaGetLastError();
    if (cudaError != cudaSuccess) {
        lastError = "CUDA错误: " + std::string(cudaGetErrorString(cudaError));
        return lastError;
    }
#endif
    
#ifdef USE_OPENCL
    // OpenCL错误处理
    // 这里需要实现OpenCL错误获取
#endif
    
    return "";
}

void clearLastError() {
#ifdef USE_CUDA
    cudaGetLastError();  // 清除CUDA错误
#endif
    
#ifdef USE_OPENCL
    // 清除OpenCL错误
#endif
}

} // namespace GPUUtils
