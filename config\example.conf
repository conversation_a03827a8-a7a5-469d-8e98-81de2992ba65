# TRON 靓号地址生成器配置文件示例
# =====================================

# 搜索模式列表（每行一个）
# 支持的模式类型：
# - 前缀匹配: TMyPrefix
# - 后缀匹配: *MySuffix  
# - 通配符: T?ron* (? = 单个字符, * = 任意字符)
# - 包含匹配: 在代码中指定

# 示例模式
TMyWallet
TRich
TLucky
T888
T666

# GPU设置
use_gpu=true
gpu_ids=0,1
grid_size_x=256
grid_size_y=128
threads_per_block=256

# CPU设置  
cpu_threads=4

# 搜索设置
case_insensitive=false
stop_on_first=true
max_results=10

# 输出设置
output_file=results.txt
verbose=true

# 安全设置
seed_phrase=""
verify_results=true

# 性能设置
batch_size=1000
update_interval=1000

# 日志设置
log_level=INFO
log_file=tron_vanity.log
