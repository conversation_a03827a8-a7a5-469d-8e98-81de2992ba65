#pragma once

#include <string>
#include <vector>
#include <regex>
#include <memory>

// 模式类型枚举
enum class PatternType {
    PREFIX,         // 前缀匹配
    SUFFIX,         // 后缀匹配
    CONTAINS,       // 包含匹配
    REGEX,          // 正则表达式匹配
    WILDCARD        // 通配符匹配 (? 和 *)
};

// 单个模式配置
struct Pattern {
    std::string pattern;        // 模式字符串
    PatternType type;           // 模式类型
    bool caseInsensitive;       // 大小写不敏感
    double difficulty;          // 难度估算
    uint64_t expectedAttempts;  // 预期尝试次数
    
    Pattern() : type(PatternType::PREFIX), caseInsensitive(false), 
                difficulty(0.0), expectedAttempts(0) {}
    
    Pattern(const std::string& pat, PatternType t = PatternType::PREFIX, 
            bool ignoreCase = false) 
        : pattern(pat), type(t), caseInsensitive(ignoreCase), 
          difficulty(0.0), expectedAttempts(0) {}
};

// 模式匹配统计
struct MatchingStats {
    uint64_t totalChecks = 0;       // 总检查次数
    uint64_t matches = 0;           // 匹配次数
    double averageCheckTime = 0.0;  // 平均检查时间(微秒)
    double matchRate = 0.0;         // 匹配率
};

// 模式匹配器基类
class PatternMatcher {
public:
    PatternMatcher();
    virtual ~PatternMatcher();
    
    // 模式管理
    virtual bool addPattern(const Pattern& pattern) = 0;
    virtual bool addPattern(const std::string& pattern, PatternType type = PatternType::PREFIX, 
                           bool caseInsensitive = false) = 0;
    virtual void clearPatterns() = 0;
    virtual std::vector<Pattern> getPatterns() const = 0;
    virtual int getPatternCount() const = 0;
    
    // 匹配检查
    virtual bool isMatch(const std::string& address) const = 0;
    virtual std::vector<int> getMatchingPatterns(const std::string& address) const = 0;
    
    // 难度计算
    virtual double calculateDifficulty(const Pattern& pattern) const = 0;
    virtual uint64_t estimateAttempts(const Pattern& pattern) const = 0;
    virtual double getTotalDifficulty() const = 0;
    
    // 统计信息
    virtual MatchingStats getStats() const = 0;
    virtual void resetStats() = 0;
    
    // 优化
    virtual void optimize() = 0;

protected:
    std::vector<Pattern> m_patterns;
    mutable MatchingStats m_stats;
    
    // 工具方法
    std::string toLower(const std::string& str) const;
    bool isValidTronAddress(const std::string& address) const;
    double calculateBaseDifficulty(const std::string& pattern, PatternType type) const;
};

// 简单模式匹配器 (CPU优化)
class SimpleMatcher : public PatternMatcher {
public:
    SimpleMatcher();
    ~SimpleMatcher() override;
    
    // PatternMatcher接口实现
    bool addPattern(const Pattern& pattern) override;
    bool addPattern(const std::string& pattern, PatternType type = PatternType::PREFIX, 
                   bool caseInsensitive = false) override;
    void clearPatterns() override;
    std::vector<Pattern> getPatterns() const override;
    int getPatternCount() const override;
    
    bool isMatch(const std::string& address) const override;
    std::vector<int> getMatchingPatterns(const std::string& address) const override;
    
    double calculateDifficulty(const Pattern& pattern) const override;
    uint64_t estimateAttempts(const Pattern& pattern) const override;
    double getTotalDifficulty() const override;
    
    MatchingStats getStats() const override;
    void resetStats() override;
    
    void optimize() override;

private:
    // 预处理的模式数据
    struct ProcessedPattern {
        std::string processedPattern;
        PatternType type;
        bool caseInsensitive;
        std::unique_ptr<std::regex> regexPattern;
        double difficulty;
        uint64_t expectedAttempts;
    };
    
    std::vector<ProcessedPattern> m_processedPatterns;
    
    void preprocessPattern(const Pattern& pattern);
    bool matchPrefix(const std::string& address, const ProcessedPattern& pattern) const;
    bool matchSuffix(const std::string& address, const ProcessedPattern& pattern) const;
    bool matchContains(const std::string& address, const ProcessedPattern& pattern) const;
    bool matchWildcard(const std::string& address, const ProcessedPattern& pattern) const;
    bool matchRegex(const std::string& address, const ProcessedPattern& pattern) const;
    
    std::string wildcardToRegex(const std::string& wildcard) const;
};

// 高性能模式匹配器 (使用字典树等优化结构)
class OptimizedMatcher : public PatternMatcher {
public:
    OptimizedMatcher();
    ~OptimizedMatcher() override;
    
    // PatternMatcher接口实现
    bool addPattern(const Pattern& pattern) override;
    bool addPattern(const std::string& pattern, PatternType type = PatternType::PREFIX, 
                   bool caseInsensitive = false) override;
    void clearPatterns() override;
    std::vector<Pattern> getPatterns() const override;
    int getPatternCount() const override;
    
    bool isMatch(const std::string& address) const override;
    std::vector<int> getMatchingPatterns(const std::string& address) const override;
    
    double calculateDifficulty(const Pattern& pattern) const override;
    uint64_t estimateAttempts(const Pattern& pattern) const override;
    double getTotalDifficulty() const override;
    
    MatchingStats getStats() const override;
    void resetStats() override;
    
    void optimize() override;

private:
    // 字典树节点
    struct TrieNode {
        std::vector<std::unique_ptr<TrieNode>> children;
        std::vector<int> patternIndices;  // 在此节点结束的模式索引
        bool isEndOfPattern;
        
        TrieNode() : isEndOfPattern(false) {
            children.resize(58, nullptr);  // Base58字符集大小
        }
    };
    
    std::unique_ptr<TrieNode> m_prefixTrie;    // 前缀字典树
    std::unique_ptr<TrieNode> m_suffixTrie;    // 后缀字典树
    
    // 其他模式类型的存储
    std::vector<Pattern> m_containsPatterns;
    std::vector<Pattern> m_regexPatterns;
    std::vector<Pattern> m_wildcardPatterns;
    
    void buildTries();
    void insertPrefix(const std::string& pattern, int index);
    void insertSuffix(const std::string& pattern, int index);
    
    bool searchPrefix(const std::string& address) const;
    bool searchSuffix(const std::string& address) const;
    
    int base58CharToIndex(char c) const;
    char indexToBase58Char(int index) const;
    
    std::string reverseString(const std::string& str) const;
};

// GPU模式匹配器 (为GPU内核优化)
class GPUMatcher : public PatternMatcher {
public:
    GPUMatcher();
    ~GPUMatcher() override;
    
    // PatternMatcher接口实现
    bool addPattern(const Pattern& pattern) override;
    bool addPattern(const std::string& pattern, PatternType type = PatternType::PREFIX, 
                   bool caseInsensitive = false) override;
    void clearPatterns() override;
    std::vector<Pattern> getPatterns() const override;
    int getPatternCount() const override;
    
    bool isMatch(const std::string& address) const override;
    std::vector<int> getMatchingPatterns(const std::string& address) const override;
    
    double calculateDifficulty(const Pattern& pattern) const override;
    uint64_t estimateAttempts(const Pattern& pattern) const override;
    double getTotalDifficulty() const override;
    
    MatchingStats getStats() const override;
    void resetStats() override;
    
    void optimize() override;
    
    // GPU特定方法
    std::vector<uint32_t> getGPUPatternData() const;
    int getMaxPatternLength() const;
    bool prepareForGPU();

private:
    // GPU优化的模式数据结构
    struct GPUPattern {
        char pattern[35];       // 最大TRON地址长度
        uint8_t length;
        uint8_t type;          // PatternType转换为数字
        uint8_t caseInsensitive;
        uint8_t reserved;
    };
    
    std::vector<GPUPattern> m_gpuPatterns;
    bool m_gpuReady;
    
    void convertToGPUFormat();
};

// 模式匹配器工厂
class MatcherFactory {
public:
    enum MatcherType {
        SIMPLE_MATCHER,
        OPTIMIZED_MATCHER,
        GPU_MATCHER
    };
    
    static std::unique_ptr<PatternMatcher> createMatcher(MatcherType type);
    static MatcherType getBestMatcher(int patternCount, bool useGPU = false);
};

// 模式工具函数
namespace PatternUtils {
    // 模式验证
    bool isValidPattern(const std::string& pattern, PatternType type);
    bool isValidTronPattern(const std::string& pattern);
    
    // 难度计算
    double calculatePatternDifficulty(const std::string& pattern, PatternType type);
    uint64_t estimateSearchTime(double difficulty, double hashRate);
    
    // 模式转换
    std::string normalizePattern(const std::string& pattern, bool caseInsensitive);
    PatternType detectPatternType(const std::string& pattern);
    
    // 统计分析
    std::vector<std::string> generateSimilarPatterns(const std::string& pattern, int count = 10);
    double calculateSuccessProbability(uint64_t attempts, double difficulty);
}
