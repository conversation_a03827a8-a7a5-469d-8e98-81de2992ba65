#include "Hash.h"
#include <cstring>
#include <sstream>
#include <iomanip>

// Keccak256轮常数
const uint64_t Keccak256::ROUND_CONSTANTS[24] = {
    0x0000000000000001ULL, 0x0000000000008082ULL, 0x800000000000808aULL,
    0x8000000080008000ULL, 0x000000000000808bULL, 0x0000000080000001ULL,
    0x8000000080008081ULL, 0x8000000000008009ULL, 0x000000000000008aULL,
    0x0000000000000088ULL, 0x0000000080008009ULL, 0x8000000000008003ULL,
    0x8000000000008002ULL, 0x8000000000000080ULL, 0x000000000000800aULL,
    0x800000008000000aULL, 0x8000000080008081ULL, 0x8000000000008080ULL,
    0x0000000080000001ULL, 0x8000000080008008ULL, 0x8000000000000000ULL,
    0x0000000080008082ULL, 0x0000000080000082ULL, 0x8000000080008003ULL
};

// Rho步骤的偏移量
const int Keccak256::RHO_OFFSETS[24] = {
    1, 3, 6, 10, 15, 21, 28, 36, 45, 55, 2, 14,
    27, 41, 56, 8, 25, 43, 62, 18, 39, 61, 20, 44
};

Keccak256::Keccak256() {
    reset();
}

Keccak256::~Keccak256() {
    // 清零敏感数据
    memset(m_state, 0, sizeof(m_state));
    memset(m_buffer, 0, sizeof(m_buffer));
}

// 一次性哈希
std::vector<uint8_t> Keccak256::hash(const std::vector<uint8_t>& data) {
    return hash(data.data(), data.size());
}

std::vector<uint8_t> Keccak256::hash(const uint8_t* data, size_t length) {
    Keccak256 hasher;
    hasher.update(data, length);
    return hasher.finalize();
}

std::string Keccak256::hashHex(const std::vector<uint8_t>& data) {
    std::vector<uint8_t> hash_result = hash(data);
    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (uint8_t byte : hash_result) {
        ss << std::setw(2) << static_cast<int>(byte);
    }
    return ss.str();
}

// 重置状态
void Keccak256::reset() {
    memset(m_state, 0, sizeof(m_state));
    memset(m_buffer, 0, sizeof(m_buffer));
    m_bufferSize = 0;
    m_finalized = false;
}

// 更新数据
void Keccak256::update(const std::vector<uint8_t>& data) {
    update(data.data(), data.size());
}

void Keccak256::update(const uint8_t* data, size_t length) {
    if (m_finalized) {
        throw std::runtime_error("Cannot update finalized hash");
    }
    
    size_t offset = 0;
    
    // 处理缓冲区中的剩余数据
    if (m_bufferSize > 0) {
        size_t needed = BLOCK_SIZE - m_bufferSize;
        size_t available = std::min(needed, length);
        
        memcpy(m_buffer + m_bufferSize, data, available);
        m_bufferSize += available;
        offset += available;
        
        if (m_bufferSize == BLOCK_SIZE) {
            processBlock();
            m_bufferSize = 0;
        }
    }
    
    // 处理完整的块
    while (offset + BLOCK_SIZE <= length) {
        memcpy(m_buffer, data + offset, BLOCK_SIZE);
        processBlock();
        offset += BLOCK_SIZE;
    }
    
    // 保存剩余数据到缓冲区
    if (offset < length) {
        m_bufferSize = length - offset;
        memcpy(m_buffer, data + offset, m_bufferSize);
    }
}

// 完成哈希计算
std::vector<uint8_t> Keccak256::finalize() {
    if (m_finalized) {
        throw std::runtime_error("Hash already finalized");
    }
    
    // 添加填充
    m_buffer[m_bufferSize] = 0x01;  // Keccak256使用0x01填充
    memset(m_buffer + m_bufferSize + 1, 0, BLOCK_SIZE - m_bufferSize - 1);
    m_buffer[BLOCK_SIZE - 1] |= 0x80;  // 设置最后一位
    
    // 处理最后一个块
    processBlock();
    
    // 提取哈希值
    std::vector<uint8_t> result(HASH_SIZE);
    for (int i = 0; i < HASH_SIZE / 8; ++i) {
        uint64_t word = m_state[i];
        for (int j = 0; j < 8; ++j) {
            result[i * 8 + j] = static_cast<uint8_t>(word >> (j * 8));
        }
    }
    
    m_finalized = true;
    return result;
}

std::string Keccak256::finalizeHex() {
    std::vector<uint8_t> hash_result = finalize();
    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (uint8_t byte : hash_result) {
        ss << std::setw(2) << static_cast<int>(byte);
    }
    return ss.str();
}

// 处理一个数据块
void Keccak256::processBlock() {
    // 将输入数据异或到状态中
    for (int i = 0; i < BLOCK_SIZE / 8; ++i) {
        uint64_t word = 0;
        for (int j = 0; j < 8; ++j) {
            word |= static_cast<uint64_t>(m_buffer[i * 8 + j]) << (j * 8);
        }
        m_state[i] ^= word;
    }
    
    // 执行Keccak-f[1600]置换
    keccakF();
}

// Keccak-f[1600]置换函数
void Keccak256::keccakF() {
    for (int round = 0; round < 24; ++round) {
        theta();
        rho();
        pi();
        chi();
        iota(round);
    }
}

// Theta步骤
void Keccak256::theta() {
    uint64_t C[5], D[5];
    
    // 计算列奇偶性
    for (int x = 0; x < 5; ++x) {
        C[x] = m_state[x] ^ m_state[x + 5] ^ m_state[x + 10] ^ m_state[x + 15] ^ m_state[x + 20];
    }
    
    // 计算D
    for (int x = 0; x < 5; ++x) {
        D[x] = C[(x + 4) % 5] ^ ((C[(x + 1) % 5] << 1) | (C[(x + 1) % 5] >> 63));
    }
    
    // 应用D到状态
    for (int x = 0; x < 5; ++x) {
        for (int y = 0; y < 5; ++y) {
            m_state[y * 5 + x] ^= D[x];
        }
    }
}

// Rho步骤
void Keccak256::rho() {
    for (int i = 1; i < 25; ++i) {
        int offset = RHO_OFFSETS[i - 1];
        m_state[i] = (m_state[i] << offset) | (m_state[i] >> (64 - offset));
    }
}

// Pi步骤
void Keccak256::pi() {
    uint64_t temp[25];
    memcpy(temp, m_state, sizeof(temp));
    
    for (int x = 0; x < 5; ++x) {
        for (int y = 0; y < 5; ++y) {
            m_state[y * 5 + x] = temp[((x + 3 * y) % 5) * 5 + x];
        }
    }
}

// Chi步骤
void Keccak256::chi() {
    uint64_t temp[25];
    memcpy(temp, m_state, sizeof(temp));
    
    for (int y = 0; y < 5; ++y) {
        for (int x = 0; x < 5; ++x) {
            m_state[y * 5 + x] = temp[y * 5 + x] ^ 
                ((~temp[y * 5 + ((x + 1) % 5)]) & temp[y * 5 + ((x + 2) % 5)]);
        }
    }
}

// Iota步骤
void Keccak256::iota(int round) {
    m_state[0] ^= ROUND_CONSTANTS[round];
}
