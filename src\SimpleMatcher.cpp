#include "PatternMatcher.h"
#include "TronVanity.h"
#include <algorithm>
#include <cmath>
#include <chrono>

PatternMatcher::PatternMatcher() {
    resetStats();
}

PatternMatcher::~PatternMatcher() {
}

std::string PatternMatcher::toLower(const std::string& str) const {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

bool PatternMatcher::isValidTronAddress(const std::string& address) const {
    return TronUtils::isValidTronAddress(address);
}

double PatternMatcher::calculateBaseDifficulty(const std::string& pattern, PatternType type) const {
    if (pattern.empty()) {
        return 1.0;
    }
    
    // Base58字符集大小
    const double base58Size = 58.0;
    
    switch (type) {
        case PatternType::PREFIX:
            // 前缀难度 = 58^长度
            return std::pow(base58Size, pattern.length());
            
        case PatternType::SUFFIX:
            // 后缀难度与前缀相同
            return std::pow(base58Size, pattern.length());
            
        case PatternType::CONTAINS:
            // 包含匹配的难度较低
            return std::pow(base58Size, pattern.length()) / 10.0;
            
        case PatternType::WILDCARD: {
            // 计算通配符模式的难度
            double difficulty = 1.0;
            for (char c : pattern) {
                if (c == '?') {
                    difficulty *= base58Size;
                } else if (c == '*') {
                    difficulty *= 10.0;  // 通配符降低难度
                } else {
                    difficulty *= base58Size;
                }
            }
            return difficulty;
        }
        
        case PatternType::REGEX:
            // 正则表达式难度估算（简化）
            return std::pow(base58Size, pattern.length() / 2.0);
            
        default:
            return 1.0;
    }
}

// SimpleMatcher实现
SimpleMatcher::SimpleMatcher() {
}

SimpleMatcher::~SimpleMatcher() {
}

bool SimpleMatcher::addPattern(const Pattern& pattern) {
    if (pattern.pattern.empty()) {
        return false;
    }
    
    // 验证模式
    if (!PatternUtils::isValidTronPattern(pattern.pattern)) {
        return false;
    }
    
    m_patterns.push_back(pattern);
    preprocessPattern(pattern);
    
    return true;
}

bool SimpleMatcher::addPattern(const std::string& pattern, PatternType type, bool caseInsensitive) {
    Pattern p(pattern, type, caseInsensitive);
    p.difficulty = calculateDifficulty(p);
    p.expectedAttempts = estimateAttempts(p);
    
    return addPattern(p);
}

void SimpleMatcher::clearPatterns() {
    m_patterns.clear();
    m_processedPatterns.clear();
}

std::vector<Pattern> SimpleMatcher::getPatterns() const {
    return m_patterns;
}

int SimpleMatcher::getPatternCount() const {
    return static_cast<int>(m_patterns.size());
}

bool SimpleMatcher::isMatch(const std::string& address) const {
    auto start = std::chrono::high_resolution_clock::now();
    
    bool result = false;
    for (const auto& processedPattern : m_processedPatterns) {
        switch (processedPattern.type) {
            case PatternType::PREFIX:
                result = matchPrefix(address, processedPattern);
                break;
            case PatternType::SUFFIX:
                result = matchSuffix(address, processedPattern);
                break;
            case PatternType::CONTAINS:
                result = matchContains(address, processedPattern);
                break;
            case PatternType::WILDCARD:
                result = matchWildcard(address, processedPattern);
                break;
            case PatternType::REGEX:
                result = matchRegex(address, processedPattern);
                break;
        }
        
        if (result) {
            break;
        }
    }
    
    // 更新统计信息
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    m_stats.totalChecks++;
    if (result) {
        m_stats.matches++;
    }
    
    // 更新平均检查时间
    double newTime = duration.count();
    m_stats.averageCheckTime = (m_stats.averageCheckTime * (m_stats.totalChecks - 1) + newTime) / m_stats.totalChecks;
    m_stats.matchRate = static_cast<double>(m_stats.matches) / m_stats.totalChecks;
    
    return result;
}

std::vector<int> SimpleMatcher::getMatchingPatterns(const std::string& address) const {
    std::vector<int> matchingIndices;
    
    for (size_t i = 0; i < m_processedPatterns.size(); ++i) {
        const auto& processedPattern = m_processedPatterns[i];
        bool matches = false;
        
        switch (processedPattern.type) {
            case PatternType::PREFIX:
                matches = matchPrefix(address, processedPattern);
                break;
            case PatternType::SUFFIX:
                matches = matchSuffix(address, processedPattern);
                break;
            case PatternType::CONTAINS:
                matches = matchContains(address, processedPattern);
                break;
            case PatternType::WILDCARD:
                matches = matchWildcard(address, processedPattern);
                break;
            case PatternType::REGEX:
                matches = matchRegex(address, processedPattern);
                break;
        }
        
        if (matches) {
            matchingIndices.push_back(static_cast<int>(i));
        }
    }
    
    return matchingIndices;
}

double SimpleMatcher::calculateDifficulty(const Pattern& pattern) const {
    return calculateBaseDifficulty(pattern.pattern, pattern.type);
}

uint64_t SimpleMatcher::estimateAttempts(const Pattern& pattern) const {
    double difficulty = calculateDifficulty(pattern);
    return static_cast<uint64_t>(difficulty * 0.693);  // ln(2) * difficulty for 50% probability
}

double SimpleMatcher::getTotalDifficulty() const {
    if (m_patterns.empty()) {
        return 1.0;
    }
    
    // 计算组合难度（最小值）
    double minDifficulty = m_patterns[0].difficulty;
    for (const auto& pattern : m_patterns) {
        minDifficulty = std::min(minDifficulty, pattern.difficulty);
    }
    
    return minDifficulty;
}

MatchingStats SimpleMatcher::getStats() const {
    return m_stats;
}

void SimpleMatcher::resetStats() {
    m_stats = MatchingStats();
}

void SimpleMatcher::optimize() {
    // 按难度排序，优先匹配简单的模式
    std::sort(m_processedPatterns.begin(), m_processedPatterns.end(),
              [](const ProcessedPattern& a, const ProcessedPattern& b) {
                  return a.difficulty < b.difficulty;
              });
}

void SimpleMatcher::preprocessPattern(const Pattern& pattern) {
    ProcessedPattern processed;
    processed.type = pattern.type;
    processed.caseInsensitive = pattern.caseInsensitive;
    processed.difficulty = pattern.difficulty;
    processed.expectedAttempts = pattern.expectedAttempts;
    
    // 预处理模式字符串
    if (pattern.caseInsensitive) {
        processed.processedPattern = toLower(pattern.pattern);
    } else {
        processed.processedPattern = pattern.pattern;
    }
    
    // 为正则表达式和通配符创建regex对象
    if (pattern.type == PatternType::REGEX) {
        try {
            std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript;
            if (pattern.caseInsensitive) {
                flags |= std::regex_constants::icase;
            }
            processed.regexPattern = std::make_unique<std::regex>(pattern.pattern, flags);
        } catch (const std::regex_error& e) {
            // 正则表达式编译失败，回退到字符串匹配
            processed.type = PatternType::CONTAINS;
        }
    } else if (pattern.type == PatternType::WILDCARD) {
        try {
            std::string regexPattern = wildcardToRegex(processed.processedPattern);
            std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript;
            if (pattern.caseInsensitive) {
                flags |= std::regex_constants::icase;
            }
            processed.regexPattern = std::make_unique<std::regex>(regexPattern, flags);
        } catch (const std::regex_error& e) {
            // 通配符转换失败，回退到字符串匹配
            processed.type = PatternType::CONTAINS;
        }
    }
    
    m_processedPatterns.push_back(std::move(processed));
}

bool SimpleMatcher::matchPrefix(const std::string& address, const ProcessedPattern& pattern) const {
    std::string testAddress = pattern.caseInsensitive ? toLower(address) : address;
    
    if (testAddress.length() < pattern.processedPattern.length()) {
        return false;
    }
    
    return testAddress.substr(0, pattern.processedPattern.length()) == pattern.processedPattern;
}

bool SimpleMatcher::matchSuffix(const std::string& address, const ProcessedPattern& pattern) const {
    std::string testAddress = pattern.caseInsensitive ? toLower(address) : address;
    
    if (testAddress.length() < pattern.processedPattern.length()) {
        return false;
    }
    
    size_t startPos = testAddress.length() - pattern.processedPattern.length();
    return testAddress.substr(startPos) == pattern.processedPattern;
}

bool SimpleMatcher::matchContains(const std::string& address, const ProcessedPattern& pattern) const {
    std::string testAddress = pattern.caseInsensitive ? toLower(address) : address;
    
    return testAddress.find(pattern.processedPattern) != std::string::npos;
}

bool SimpleMatcher::matchWildcard(const std::string& address, const ProcessedPattern& pattern) const {
    if (pattern.regexPattern) {
        return matchRegex(address, pattern);
    }
    
    // 简单的通配符匹配实现
    std::string testAddress = pattern.caseInsensitive ? toLower(address) : address;
    return std::regex_match(testAddress, std::regex(wildcardToRegex(pattern.processedPattern)));
}

bool SimpleMatcher::matchRegex(const std::string& address, const ProcessedPattern& pattern) const {
    if (!pattern.regexPattern) {
        return false;
    }
    
    try {
        return std::regex_search(address, *pattern.regexPattern);
    } catch (const std::regex_error& e) {
        return false;
    }
}

std::string SimpleMatcher::wildcardToRegex(const std::string& wildcard) const {
    std::string regex;
    regex.reserve(wildcard.length() * 2);
    
    for (char c : wildcard) {
        switch (c) {
            case '*':
                regex += ".*";
                break;
            case '?':
                regex += ".";
                break;
            case '.':
            case '^':
            case '$':
            case '+':
            case '{':
            case '}':
            case '[':
            case ']':
            case '(':
            case ')':
            case '|':
            case '\\':
                regex += '\\';
                regex += c;
                break;
            default:
                regex += c;
                break;
        }
    }
    
    return regex;
}
