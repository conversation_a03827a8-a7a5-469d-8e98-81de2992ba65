# TRON 靓号地址生成器 (TRON Vanity Address Generator)

🚀 高性能的波场(TRON)网络靓号地址生成器，利用GPU加速实现快速生成。

## 功能特性

- ✅ 支持TRON网络地址生成
- ✅ GPU加速 (CUDA/OpenCL)
- ✅ 自定义前缀/后缀匹配
- ✅ 通配符支持 ('?' 和 '*')
- ✅ 大小写不敏感搜索
- ✅ 多GPU支持
- ✅ 实时性能统计
- ✅ 安全的私钥生成

## 技术实现

### TRON地址生成算法
1. **私钥生成**: 使用安全随机数生成256位私钥
2. **公钥计算**: 使用secp256k1椭圆曲线计算公钥 P = d × G
3. **地址生成**: 
   - 对公钥进行Keccak256哈希
   - 取哈希结果的后20字节
   - 添加0x41前缀
   - 进行Base58Check编码

### GPU加速优化
- 并行椭圆曲线点乘运算
- 优化的Keccak256哈希计算
- 高效的Base58编码
- 内存访问优化

## 系统要求

- NVIDIA GPU (支持CUDA 8.0+)
- Windows 10/11 或 Linux
- Visual Studio 2019+ (Windows) 或 GCC 7+ (Linux)
- CUDA Toolkit 10.0+

## 编译和使用

### Windows
```bash
# 使用Visual Studio编译
# 打开 TronVanity.sln
# 选择Release配置
# 编译项目
```

### Linux
```bash
# 安装依赖
sudo apt-get install build-essential cuda-toolkit

# 编译
make gpu=1 CCAP=6.0 all

# 运行
./TronVanity -gpu -stop TMyPrefix
```

## 使用示例

```bash
# 基本使用 - 搜索前缀
TronVanity.exe -gpu -stop TMyPrefix

# 搜索后缀
TronVanity.exe -gpu -stop *MyEnd

# 使用通配符
TronVanity.exe -gpu -stop T?ron*

# 大小写不敏感
TronVanity.exe -gpu -c -stop tmyprefix

# 指定GPU
TronVanity.exe -gpu -gpuId 0,1 -stop TMyPrefix

# 输出到文件
TronVanity.exe -gpu -o results.txt -stop TMyPrefix
```

## 安全提醒

⚠️ **重要安全提示**:
1. 生成的私钥和地址必须进行验证
2. 建议对生成的地址进行多签处理
3. 不要使用来路不明的版本
4. 妥善保管私钥，避免泄露

## 许可证

本项目基于 Apache 2.0 许可证开源。

## 免责声明

本程序仅用于学习和研究目的，请勿用于非法用途。使用者需自行承担使用风险。
