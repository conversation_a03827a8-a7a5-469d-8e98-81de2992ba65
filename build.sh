#!/bin/bash

# TRON 靓号地址生成器构建脚本
# ===============================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "TRON 靓号地址生成器构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -c, --clean    清理构建文件"
    echo "  -d, --debug    构建调试版本"
    echo "  -r, --release  构建发布版本"
    echo "  -t, --test     运行测试"
    echo "  -g, --gpu      启用GPU支持 (CUDA)"
    echo "  -o, --opencl   启用OpenCL支持"
    echo "  --cpu-only     仅CPU版本"
    echo "  --install      安装到系统"
    echo ""
    echo "示例:"
    echo "  $0 --gpu --release    # 构建GPU加速的发布版本"
    echo "  $0 --cpu-only --debug # 构建CPU版本的调试版本"
    echo "  $0 --test             # 运行测试"
}

# 检查依赖
check_dependencies() {
    print_info "检查构建依赖..."
    
    # 检查编译器
    if ! command -v g++ &> /dev/null; then
        print_error "未找到 g++ 编译器"
        exit 1
    fi
    print_success "找到 g++ 编译器: $(g++ --version | head -n1)"
    
    # 检查make
    if ! command -v make &> /dev/null; then
        print_error "未找到 make 工具"
        exit 1
    fi
    print_success "找到 make 工具: $(make --version | head -n1)"
    
    # 检查CUDA（如果需要）
    if [ "$ENABLE_CUDA" = "true" ]; then
        if ! command -v nvcc &> /dev/null; then
            print_warning "未找到 nvcc 编译器，将禁用CUDA支持"
            ENABLE_CUDA="false"
        else
            print_success "找到 nvcc 编译器: $(nvcc --version | grep release)"
        fi
    fi
    
    # 检查OpenCL（如果需要）
    if [ "$ENABLE_OPENCL" = "true" ]; then
        if ! pkg-config --exists OpenCL; then
            print_warning "未找到 OpenCL 开发库，将禁用OpenCL支持"
            ENABLE_OPENCL="false"
        else
            print_success "找到 OpenCL 开发库"
        fi
    fi
}

# 清理构建文件
clean_build() {
    print_info "清理构建文件..."
    make clean
    print_success "清理完成"
}

# 构建项目
build_project() {
    print_info "开始构建项目..."
    
    # 设置构建参数
    MAKE_ARGS=""
    
    if [ "$BUILD_TYPE" = "debug" ]; then
        MAKE_ARGS="$MAKE_ARGS debug"
        print_info "构建调试版本"
    elif [ "$BUILD_TYPE" = "release" ]; then
        MAKE_ARGS="$MAKE_ARGS release"
        print_info "构建发布版本"
    fi
    
    if [ "$ENABLE_CUDA" = "true" ]; then
        MAKE_ARGS="$MAKE_ARGS cuda"
        print_info "启用CUDA支持"
    elif [ "$ENABLE_OPENCL" = "true" ]; then
        MAKE_ARGS="$MAKE_ARGS opencl"
        print_info "启用OpenCL支持"
    else
        MAKE_ARGS="$MAKE_ARGS cpu"
        print_info "构建CPU版本"
    fi
    
    # 执行构建
    if make $MAKE_ARGS; then
        print_success "构建完成"
    else
        print_error "构建失败"
        exit 1
    fi
}

# 运行测试
run_tests() {
    print_info "运行测试..."
    
    # 构建测试程序
    if [ ! -f "bin/TronVanity" ]; then
        print_info "先构建主程序..."
        build_project
    fi
    
    # 编译测试程序
    print_info "编译测试程序..."
    mkdir -p obj test_bin
    
    g++ -std=c++17 -O2 -Isrc test/test_tron_vanity.cpp \
        src/TronUtils.cpp src/Base58.cpp src/Keccak256.cpp src/SHA256.cpp \
        src/Int256.cpp src/SimpleMatcher.cpp src/PatternUtils.cpp \
        src/GPUEngineFactory.cpp -lpthread -o test_bin/test_tron_vanity
    
    if [ $? -eq 0 ]; then
        print_success "测试程序编译完成"
        
        # 运行测试
        print_info "执行测试..."
        if ./test_bin/test_tron_vanity; then
            print_success "所有测试通过"
        else
            print_error "测试失败"
            exit 1
        fi
    else
        print_error "测试程序编译失败"
        exit 1
    fi
}

# 安装程序
install_program() {
    print_info "安装程序到系统..."
    
    if [ ! -f "bin/TronVanity" ]; then
        print_error "未找到可执行文件，请先构建程序"
        exit 1
    fi
    
    # 检查权限
    if [ "$EUID" -ne 0 ]; then
        print_warning "需要root权限进行安装，尝试使用sudo..."
        sudo make install
    else
        make install
    fi
    
    print_success "安装完成"
}

# 默认参数
BUILD_TYPE="release"
ENABLE_CUDA="false"
ENABLE_OPENCL="false"
RUN_TESTS="false"
CLEAN_BUILD="false"
INSTALL_PROGRAM="false"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--clean)
            CLEAN_BUILD="true"
            shift
            ;;
        -d|--debug)
            BUILD_TYPE="debug"
            shift
            ;;
        -r|--release)
            BUILD_TYPE="release"
            shift
            ;;
        -t|--test)
            RUN_TESTS="true"
            shift
            ;;
        -g|--gpu)
            ENABLE_CUDA="true"
            shift
            ;;
        -o|--opencl)
            ENABLE_OPENCL="true"
            shift
            ;;
        --cpu-only)
            ENABLE_CUDA="false"
            ENABLE_OPENCL="false"
            shift
            ;;
        --install)
            INSTALL_PROGRAM="true"
            shift
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主流程
main() {
    print_info "TRON 靓号地址生成器构建脚本"
    print_info "=============================="
    
    # 检查依赖
    check_dependencies
    
    # 清理（如果需要）
    if [ "$CLEAN_BUILD" = "true" ]; then
        clean_build
    fi
    
    # 构建项目
    if [ "$RUN_TESTS" = "false" ] && [ "$CLEAN_BUILD" = "false" ] || [ "$INSTALL_PROGRAM" = "true" ]; then
        build_project
    fi
    
    # 运行测试
    if [ "$RUN_TESTS" = "true" ]; then
        run_tests
    fi
    
    # 安装程序
    if [ "$INSTALL_PROGRAM" = "true" ]; then
        install_program
    fi
    
    print_success "所有操作完成"
}

# 执行主流程
main
