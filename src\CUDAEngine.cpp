#ifdef USE_CUDA

#include "GPUEngine.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <cstring>

// 外部CUDA函数声明
extern "C" bool launchTronVanityKernel(
    dim3 gridSize, dim3 blockSize,
    uint8_t* d_privateKeys, uint8_t* d_publicKeys, char* d_addresses,
    bool* d_found, uint64_t* d_iterations, uint64_t startNonce
);

extern "C" bool setPatternData(const std::vector<std::string>& patterns);

CUDAEngine::CUDAEngine() 
    : m_deviceId(-1), m_stream(nullptr),
      d_privateKeys(nullptr), d_publicKeys(nullptr), d_addresses(nullptr),
      d_results(nullptr), d_patterns(nullptr), d_found(nullptr),
      h_results(nullptr) {
}

CUDAEngine::~CUDAEngine() {
    cleanup();
}

bool CUDAEngine::initialize() {
    // 检查CUDA设备数量
    int deviceCount;
    cudaError_t error = cudaGetDeviceCount(&deviceCount);
    if (error != cudaSuccess || deviceCount == 0) {
        std::cerr << "未找到CUDA设备: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    // 选择默认设备
    if (!selectDevice(0)) {
        return false;
    }
    
    // 创建CUDA流
    error = cudaStreamCreate(&m_stream);
    if (error != cudaSuccess) {
        std::cerr << "创建CUDA流失败: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    // 创建内存管理器
    m_memoryManager = std::make_unique<GPUMemoryManager>(m_deviceId);
    
    std::cout << "CUDA引擎初始化成功" << std::endl;
    return true;
}

void CUDAEngine::cleanup() {
    stopSearch();
    freeMemory();
    
    if (m_stream) {
        cudaStreamDestroy(m_stream);
        m_stream = nullptr;
    }
    
    if (m_deviceId >= 0) {
        cudaDeviceReset();
        m_deviceId = -1;
    }
}

std::vector<GPUDeviceInfo> CUDAEngine::getDeviceList() {
    std::vector<GPUDeviceInfo> devices;
    
    int deviceCount;
    cudaError_t error = cudaGetDeviceCount(&deviceCount);
    if (error != cudaSuccess) {
        return devices;
    }
    
    for (int i = 0; i < deviceCount; ++i) {
        cudaDeviceProp prop;
        error = cudaGetDeviceProperties(&prop, i);
        if (error != cudaSuccess) {
            continue;
        }
        
        GPUDeviceInfo info;
        info.deviceId = i;
        info.name = prop.name;
        info.globalMemory = prop.totalGlobalMem;
        info.sharedMemory = prop.sharedMemPerBlock;
        info.multiProcessors = prop.multiProcessorCount;
        info.maxThreadsPerBlock = prop.maxThreadsPerBlock;
        info.maxThreadsPerMultiProcessor = prop.maxThreadsPerMultiProcessor;
        info.warpSize = prop.warpSize;
        info.computeCapabilityMajor = prop.major;
        info.computeCapabilityMinor = prop.minor;
        info.isAvailable = true;
        
        devices.push_back(info);
    }
    
    return devices;
}

bool CUDAEngine::selectDevice(int deviceId) {
    cudaError_t error = cudaSetDevice(deviceId);
    if (error != cudaSuccess) {
        std::cerr << "选择CUDA设备失败: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    error = cudaGetDeviceProperties(&m_deviceProp, deviceId);
    if (error != cudaSuccess) {
        std::cerr << "获取设备属性失败: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    m_deviceId = deviceId;
    
    std::cout << "选择CUDA设备 " << deviceId << ": " << m_deviceProp.name << std::endl;
    std::cout << "计算能力: " << m_deviceProp.major << "." << m_deviceProp.minor << std::endl;
    std::cout << "全局内存: " << (m_deviceProp.totalGlobalMem / 1024 / 1024) << " MB" << std::endl;
    std::cout << "多处理器: " << m_deviceProp.multiProcessorCount << std::endl;
    
    return true;
}

GPUDeviceInfo CUDAEngine::getCurrentDevice() {
    GPUDeviceInfo info;
    if (m_deviceId >= 0) {
        info.deviceId = m_deviceId;
        info.name = m_deviceProp.name;
        info.globalMemory = m_deviceProp.totalGlobalMem;
        info.sharedMemory = m_deviceProp.sharedMemPerBlock;
        info.multiProcessors = m_deviceProp.multiProcessorCount;
        info.maxThreadsPerBlock = m_deviceProp.maxThreadsPerBlock;
        info.maxThreadsPerMultiProcessor = m_deviceProp.maxThreadsPerMultiProcessor;
        info.warpSize = m_deviceProp.warpSize;
        info.computeCapabilityMajor = m_deviceProp.major;
        info.computeCapabilityMinor = m_deviceProp.minor;
        info.isAvailable = true;
    }
    return info;
}

bool CUDAEngine::setKernelConfig(const GPUKernelConfig& config) {
    m_config = config;
    
    // 验证配置参数
    if (m_config.blockSizeX * m_config.blockSizeY > m_deviceProp.maxThreadsPerBlock) {
        std::cerr << "块大小超过设备限制" << std::endl;
        return false;
    }
    
    if (m_config.gridSizeX > m_deviceProp.maxGridSize[0] ||
        m_config.gridSizeY > m_deviceProp.maxGridSize[1]) {
        std::cerr << "网格大小超过设备限制" << std::endl;
        return false;
    }
    
    return true;
}

GPUKernelConfig CUDAEngine::getOptimalConfig() {
    GPUKernelConfig config;
    
    // 根据设备属性计算最优配置
    config.blockSizeX = std::min(256, m_deviceProp.maxThreadsPerBlock);
    config.blockSizeY = 1;
    
    // 网格大小基于多处理器数量
    config.gridSizeX = m_deviceProp.multiProcessorCount * 8;
    config.gridSizeY = 1;
    
    // 共享内存大小
    config.sharedMemorySize = m_deviceProp.sharedMemPerBlock / 2;
    
    return config;
}

bool CUDAEngine::startSearch(const std::vector<std::string>& patterns) {
    if (m_running) {
        return false;
    }
    
    // 设置模式数据
    if (!setPatternData(patterns)) {
        std::cerr << "设置模式数据失败" << std::endl;
        return false;
    }
    
    // 分配GPU内存
    if (!allocateMemory()) {
        std::cerr << "分配GPU内存失败" << std::endl;
        return false;
    }
    
    m_running = true;
    m_iterations = 0;
    
    // 启动搜索线程
    std::thread searchThread([this]() {
        uint64_t nonce = 0;
        auto startTime = std::chrono::steady_clock::now();
        
        while (m_running) {
            // 启动内核
            launchKernel();
            
            // 检查结果
            if (checkResults()) {
                // 找到匹配，可以选择停止或继续
                if (m_resultCallback) {
                    // 通知找到结果
                }
            }
            
            // 更新统计信息
            updateStats();
            
            nonce += m_config.gridSizeX * m_config.gridSizeY * 
                     m_config.blockSizeX * m_config.blockSizeY;
            
            // 避免过度占用GPU
            std::this_thread::sleep_for(std::chrono::microseconds(100));
        }
    });
    
    searchThread.detach();
    return true;
}

void CUDAEngine::stopSearch() {
    m_running = false;
    
    // 等待内核完成
    if (m_deviceId >= 0) {
        cudaDeviceSynchronize();
    }
}

bool CUDAEngine::isSearching() const {
    return m_running;
}

std::vector<GPUSearchResult> CUDAEngine::getResults() {
    std::vector<GPUSearchResult> results;
    
    if (h_results) {
        // 从主机内存读取结果
        for (int i = 0; i < 1000; ++i) {  // 最多1000个结果
            if (h_results[i].found) {
                results.push_back(h_results[i]);
            }
        }
    }
    
    return results;
}

uint64_t CUDAEngine::getIterationCount() const {
    return m_iterations;
}

double CUDAEngine::getHashRate() const {
    return m_hashRate;
}

void CUDAEngine::updateStats() {
    static auto lastUpdate = std::chrono::steady_clock::now();
    auto now = std::chrono::steady_clock::now();
    
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastUpdate);
    if (elapsed.count() >= 1000) {  // 每秒更新一次
        uint64_t totalThreads = m_config.gridSizeX * m_config.gridSizeY * 
                               m_config.blockSizeX * m_config.blockSizeY;
        
        m_hashRate = totalThreads * 1000.0 / elapsed.count();  // keys/s
        m_iterations += totalThreads;
        
        if (m_progressCallback) {
            m_progressCallback(m_iterations, m_hashRate);
        }
        
        lastUpdate = now;
    }
}

bool CUDAEngine::allocateMemory() {
    const int maxResults = 1000;
    const int maxPatterns = 32;
    
    try {
        // 分配设备内存
        d_privateKeys = static_cast<uint8_t*>(
            m_memoryManager->allocateDevice(maxResults * 32));
        d_publicKeys = static_cast<uint8_t*>(
            m_memoryManager->allocateDevice(maxResults * 64));
        d_addresses = static_cast<char*>(
            m_memoryManager->allocateDevice(maxResults * 35));
        d_results = static_cast<GPUSearchResult*>(
            m_memoryManager->allocateDevice(maxResults * sizeof(GPUSearchResult)));
        d_found = static_cast<bool*>(
            m_memoryManager->allocateDevice(2 * sizeof(bool)));
        
        // 分配主机内存
        h_results = static_cast<GPUSearchResult*>(
            m_memoryManager->allocateHostPinned(maxResults * sizeof(GPUSearchResult)));
        
        // 初始化内存
        cudaMemset(d_found, 0, 2 * sizeof(bool));
        cudaMemset(h_results, 0, maxResults * sizeof(GPUSearchResult));
        
        return true;
    } catch (...) {
        freeMemory();
        return false;
    }
}

void CUDAEngine::freeMemory() {
    if (m_memoryManager) {
        if (d_privateKeys) m_memoryManager->freeDevice(d_privateKeys);
        if (d_publicKeys) m_memoryManager->freeDevice(d_publicKeys);
        if (d_addresses) m_memoryManager->freeDevice(d_addresses);
        if (d_results) m_memoryManager->freeDevice(d_results);
        if (d_found) m_memoryManager->freeDevice(d_found);
        if (h_results) m_memoryManager->freeHostPinned(h_results);
    }
    
    d_privateKeys = nullptr;
    d_publicKeys = nullptr;
    d_addresses = nullptr;
    d_results = nullptr;
    d_found = nullptr;
    h_results = nullptr;
}

void CUDAEngine::launchKernel() {
    dim3 gridSize(m_config.gridSizeX, m_config.gridSizeY);
    dim3 blockSize(m_config.blockSizeX, m_config.blockSizeY);
    
    static uint64_t nonce = 0;
    nonce += gridSize.x * gridSize.y * blockSize.x * blockSize.y;
    
    launchTronVanityKernel(
        gridSize, blockSize,
        d_privateKeys, d_publicKeys, d_addresses,
        d_found, nullptr, nonce
    );
    
    // 同步等待内核完成
    cudaStreamSynchronize(m_stream);
}

bool CUDAEngine::checkResults() {
    // 检查是否找到匹配
    bool found[2];
    cudaMemcpy(found, d_found, 2 * sizeof(bool), cudaMemcpyDeviceToHost);
    
    if (found[0]) {
        // 复制结果到主机
        int resultCount = std::min(static_cast<int>(found[1]), 1000);
        
        // 这里需要从设备内存构造GPUSearchResult结构
        // 简化实现，实际需要完整的数据传输
        
        return true;
    }
    
    return false;
}

#endif // USE_CUDA
