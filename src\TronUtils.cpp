#include "TronVanity.h"
#include "Hash.h"
#include "Secp256k1.h"
#include <random>
#include <chrono>
#include <sstream>
#include <iomanip>
#include <algorithm>

namespace TronUtils {

// 生成安全的随机私钥
std::vector<uint8_t> generatePrivateKey() {
    std::vector<uint8_t> privateKey(32);
    
    // 使用高质量随机数生成器
    std::random_device rd;
    std::mt19937_64 gen(rd());
    
    // 添加时间戳作为额外熵源
    auto now = std::chrono::high_resolution_clock::now();
    auto timestamp = now.time_since_epoch().count();
    gen.seed(gen() ^ timestamp);
    
    // 生成随机字节
    std::uniform_int_distribution<uint8_t> dis(0, 255);
    for (int i = 0; i < 32; ++i) {
        privateKey[i] = dis(gen);
    }
    
    // 确保私钥在有效范围内 (1 < privateKey < n)
    // secp256k1的阶数n
    const uint8_t n[32] = {
        0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE,
        0xBA, 0xAE, 0xDC, 0xE6, 0xAF, 0x48, 0xA0, 0x3B,
        0xBF, 0xD2, 0x5E, 0x8C, 0xD0, 0x36, 0x41, 0x41
    };
    
    // 检查私钥是否小于n
    bool isValid = false;
    for (int i = 0; i < 32; ++i) {
        if (privateKey[i] < n[i]) {
            isValid = true;
            break;
        } else if (privateKey[i] > n[i]) {
            break;
        }
    }
    
    // 如果私钥无效，递归重新生成
    if (!isValid) {
        return generatePrivateKey();
    }
    
    // 确保私钥不为0
    bool isZero = true;
    for (int i = 0; i < 32; ++i) {
        if (privateKey[i] != 0) {
            isZero = false;
            break;
        }
    }
    
    if (isZero) {
        return generatePrivateKey();
    }
    
    return privateKey;
}

// 从私钥计算公钥
std::vector<uint8_t> privateKeyToPublicKey(const std::vector<uint8_t>& privateKey) {
    if (privateKey.size() != 32) {
        throw std::invalid_argument("Private key must be 32 bytes");
    }
    
    // 创建secp256k1曲线实例
    Secp256k1Curve curve;
    
    // 将私钥转换为Int256
    Int256 privKey(privateKey.data());
    
    // 计算公钥点 P = privKey * G
    ECPoint publicKeyPoint = curve.privateKeyToPublicKey(privKey);
    
    // 转换为未压缩格式 (64字节)
    return publicKeyPoint.toBytes(false);
}

// 从公钥生成TRON地址
std::string publicKeyToAddress(const std::vector<uint8_t>& publicKey) {
    if (publicKey.size() != 64) {
        throw std::invalid_argument("Public key must be 64 bytes (uncompressed)");
    }
    
    // 1. 对公钥进行Keccak256哈希
    std::vector<uint8_t> hash = Keccak256::hash(publicKey);
    
    // 2. 取哈希结果的后20字节
    std::vector<uint8_t> addressBytes(21);
    addressBytes[0] = TRON_ADDRESS_PREFIX; // 0x41
    std::copy(hash.end() - 20, hash.end(), addressBytes.begin() + 1);
    
    // 3. 进行Base58Check编码
    return Base58::encodeCheck(addressBytes);
}

// 私钥转WIF格式 (TRON使用与Bitcoin相同的WIF格式)
std::string privateKeyToWIF(const std::vector<uint8_t>& privateKey) {
    if (privateKey.size() != 32) {
        throw std::invalid_argument("Private key must be 32 bytes");
    }
    
    // TRON使用0x80作为WIF前缀 (与Bitcoin相同)
    std::vector<uint8_t> wifData(33);
    wifData[0] = 0x80;
    std::copy(privateKey.begin(), privateKey.end(), wifData.begin() + 1);
    
    return Base58::encodeCheck(wifData);
}

// 字节数组转十六进制字符串
std::string bytesToHex(const std::vector<uint8_t>& bytes) {
    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (uint8_t byte : bytes) {
        ss << std::setw(2) << static_cast<int>(byte);
    }
    return ss.str();
}

// 十六进制字符串转字节数组
std::vector<uint8_t> hexToBytes(const std::string& hex) {
    if (hex.length() % 2 != 0) {
        throw std::invalid_argument("Hex string length must be even");
    }
    
    std::vector<uint8_t> bytes;
    bytes.reserve(hex.length() / 2);
    
    for (size_t i = 0; i < hex.length(); i += 2) {
        std::string byteString = hex.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(std::stoul(byteString, nullptr, 16));
        bytes.push_back(byte);
    }
    
    return bytes;
}

// Base58Check编码
std::string base58CheckEncode(const std::vector<uint8_t>& data) {
    return Base58::encodeCheck(data);
}

// Base58Check解码
std::vector<uint8_t> base58CheckDecode(const std::string& encoded) {
    return Base58::decodeCheck(encoded);
}

// 验证TRON地址格式
bool isValidTronAddress(const std::string& address) {
    // 检查长度
    if (address.length() != TRON_BASE58_LENGTH) {
        return false;
    }
    
    // 检查是否以'T'开头
    if (address[0] != 'T') {
        return false;
    }
    
    // 尝试Base58Check解码
    try {
        std::vector<uint8_t> decoded = Base58::decodeCheck(address);
        
        // 检查解码后的长度和前缀
        if (decoded.size() != TRON_ADDRESS_LENGTH || decoded[0] != TRON_ADDRESS_PREFIX) {
            return false;
        }
        
        return true;
    } catch (...) {
        return false;
    }
}

// 获取当前时间(毫秒)
double getCurrentTime() {
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration<double>(duration).count();
}

// 格式化时间显示
std::string formatTime(double seconds) {
    if (seconds < 60) {
        return std::to_string(static_cast<int>(seconds)) + "s";
    } else if (seconds < 3600) {
        int minutes = static_cast<int>(seconds / 60);
        int secs = static_cast<int>(seconds) % 60;
        return std::to_string(minutes) + "m " + std::to_string(secs) + "s";
    } else if (seconds < 86400) {
        int hours = static_cast<int>(seconds / 3600);
        int minutes = static_cast<int>(seconds / 60) % 60;
        return std::to_string(hours) + "h " + std::to_string(minutes) + "m";
    } else {
        int days = static_cast<int>(seconds / 86400);
        int hours = static_cast<int>(seconds / 3600) % 24;
        return std::to_string(days) + "d " + std::to_string(hours) + "h";
    }
}

// 格式化数字显示
std::string formatNumber(uint64_t number) {
    if (number < 1000) {
        return std::to_string(number);
    } else if (number < 1000000) {
        double k = number / 1000.0;
        std::stringstream ss;
        ss << std::fixed << std::setprecision(1) << k << "K";
        return ss.str();
    } else if (number < 1000000000) {
        double m = number / 1000000.0;
        std::stringstream ss;
        ss << std::fixed << std::setprecision(1) << m << "M";
        return ss.str();
    } else {
        double g = number / 1000000000.0;
        std::stringstream ss;
        ss << std::fixed << std::setprecision(1) << g << "G";
        return ss.str();
    }
}

} // namespace TronUtils
