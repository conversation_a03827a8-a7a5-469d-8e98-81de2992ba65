#include "Hash.h"
#include <cstring>
#include <sstream>
#include <iomanip>

// SHA256常数
const uint32_t SHA256::K[64] = {
    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
};

SHA256::SHA256() {
    reset();
}

SHA256::~SHA256() {
    // 清零敏感数据
    memset(m_state, 0, sizeof(m_state));
    memset(m_buffer, 0, sizeof(m_buffer));
}

// 一次性哈希
std::vector<uint8_t> SHA256::hash(const std::vector<uint8_t>& data) {
    return hash(data.data(), data.size());
}

std::vector<uint8_t> SHA256::hash(const uint8_t* data, size_t length) {
    SHA256 hasher;
    hasher.update(data, length);
    return hasher.finalize();
}

std::string SHA256::hashHex(const std::vector<uint8_t>& data) {
    std::vector<uint8_t> hash_result = hash(data);
    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (uint8_t byte : hash_result) {
        ss << std::setw(2) << static_cast<int>(byte);
    }
    return ss.str();
}

// 双重SHA256
std::vector<uint8_t> SHA256::doubleHash(const std::vector<uint8_t>& data) {
    std::vector<uint8_t> firstHash = hash(data);
    return hash(firstHash);
}

// 重置状态
void SHA256::reset() {
    // SHA256初始哈希值
    m_state[0] = 0x6a09e667;
    m_state[1] = 0xbb67ae85;
    m_state[2] = 0x3c6ef372;
    m_state[3] = 0xa54ff53a;
    m_state[4] = 0x510e527f;
    m_state[5] = 0x9b05688c;
    m_state[6] = 0x1f83d9ab;
    m_state[7] = 0x5be0cd19;
    
    memset(m_buffer, 0, sizeof(m_buffer));
    m_bufferSize = 0;
    m_totalLength = 0;
    m_finalized = false;
}

// 更新数据
void SHA256::update(const std::vector<uint8_t>& data) {
    update(data.data(), data.size());
}

void SHA256::update(const uint8_t* data, size_t length) {
    if (m_finalized) {
        throw std::runtime_error("Cannot update finalized hash");
    }
    
    m_totalLength += length;
    size_t offset = 0;
    
    // 处理缓冲区中的剩余数据
    if (m_bufferSize > 0) {
        size_t needed = BLOCK_SIZE - m_bufferSize;
        size_t available = std::min(needed, length);
        
        memcpy(m_buffer + m_bufferSize, data, available);
        m_bufferSize += available;
        offset += available;
        
        if (m_bufferSize == BLOCK_SIZE) {
            processBlock(m_buffer);
            m_bufferSize = 0;
        }
    }
    
    // 处理完整的块
    while (offset + BLOCK_SIZE <= length) {
        processBlock(data + offset);
        offset += BLOCK_SIZE;
    }
    
    // 保存剩余数据到缓冲区
    if (offset < length) {
        m_bufferSize = length - offset;
        memcpy(m_buffer, data + offset, m_bufferSize);
    }
}

// 完成哈希计算
std::vector<uint8_t> SHA256::finalize() {
    if (m_finalized) {
        throw std::runtime_error("Hash already finalized");
    }
    
    // 添加填充
    uint64_t bitLength = m_totalLength * 8;
    
    // 添加'1'位
    m_buffer[m_bufferSize++] = 0x80;
    
    // 如果剩余空间不足8字节存储长度，先填充到块边界
    if (m_bufferSize > BLOCK_SIZE - 8) {
        memset(m_buffer + m_bufferSize, 0, BLOCK_SIZE - m_bufferSize);
        processBlock(m_buffer);
        m_bufferSize = 0;
    }
    
    // 填充零直到剩余8字节
    memset(m_buffer + m_bufferSize, 0, BLOCK_SIZE - 8 - m_bufferSize);
    
    // 添加长度（大端序）
    for (int i = 0; i < 8; ++i) {
        m_buffer[BLOCK_SIZE - 1 - i] = static_cast<uint8_t>(bitLength >> (i * 8));
    }
    
    // 处理最后一个块
    processBlock(m_buffer);
    
    // 提取哈希值（大端序）
    std::vector<uint8_t> result(HASH_SIZE);
    for (int i = 0; i < 8; ++i) {
        uint32_t word = m_state[i];
        result[i * 4 + 0] = static_cast<uint8_t>(word >> 24);
        result[i * 4 + 1] = static_cast<uint8_t>(word >> 16);
        result[i * 4 + 2] = static_cast<uint8_t>(word >> 8);
        result[i * 4 + 3] = static_cast<uint8_t>(word);
    }
    
    m_finalized = true;
    return result;
}

std::string SHA256::finalizeHex() {
    std::vector<uint8_t> hash_result = finalize();
    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (uint8_t byte : hash_result) {
        ss << std::setw(2) << static_cast<int>(byte);
    }
    return ss.str();
}

// 处理一个数据块
void SHA256::processBlock() {
    processBlock(m_buffer);
}

void SHA256::processBlock(const uint8_t* block) {
    uint32_t W[64];
    uint32_t a, b, c, d, e, f, g, h;
    
    // 准备消息调度
    for (int i = 0; i < 16; ++i) {
        W[i] = (static_cast<uint32_t>(block[i * 4 + 0]) << 24) |
               (static_cast<uint32_t>(block[i * 4 + 1]) << 16) |
               (static_cast<uint32_t>(block[i * 4 + 2]) << 8) |
               (static_cast<uint32_t>(block[i * 4 + 3]));
    }
    
    for (int i = 16; i < 64; ++i) {
        W[i] = gamma1(W[i - 2]) + W[i - 7] + gamma0(W[i - 15]) + W[i - 16];
    }
    
    // 初始化工作变量
    a = m_state[0]; b = m_state[1]; c = m_state[2]; d = m_state[3];
    e = m_state[4]; f = m_state[5]; g = m_state[6]; h = m_state[7];
    
    // 主循环
    for (int i = 0; i < 64; ++i) {
        uint32_t T1 = h + sigma1(e) + ch(e, f, g) + K[i] + W[i];
        uint32_t T2 = sigma0(a) + maj(a, b, c);
        
        h = g; g = f; f = e; e = d + T1;
        d = c; c = b; b = a; a = T1 + T2;
    }
    
    // 更新哈希值
    m_state[0] += a; m_state[1] += b; m_state[2] += c; m_state[3] += d;
    m_state[4] += e; m_state[5] += f; m_state[6] += g; m_state[7] += h;
}

// SHA256辅助函数
uint32_t SHA256::rotr(uint32_t x, int n) {
    return (x >> n) | (x << (32 - n));
}

uint32_t SHA256::ch(uint32_t x, uint32_t y, uint32_t z) {
    return (x & y) ^ (~x & z);
}

uint32_t SHA256::maj(uint32_t x, uint32_t y, uint32_t z) {
    return (x & y) ^ (x & z) ^ (y & z);
}

uint32_t SHA256::sigma0(uint32_t x) {
    return rotr(x, 2) ^ rotr(x, 13) ^ rotr(x, 22);
}

uint32_t SHA256::sigma1(uint32_t x) {
    return rotr(x, 6) ^ rotr(x, 11) ^ rotr(x, 25);
}

uint32_t SHA256::gamma0(uint32_t x) {
    return rotr(x, 7) ^ rotr(x, 18) ^ (x >> 3);
}

uint32_t SHA256::gamma1(uint32_t x) {
    return rotr(x, 17) ^ rotr(x, 19) ^ (x >> 10);
}
