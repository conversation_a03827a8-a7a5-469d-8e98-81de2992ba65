# TRON 靓号地址生成器使用示例

## 基本使用

### 1. 生成简单前缀地址

```bash
# 生成以"TTest"开头的地址
./TronVanity TTest

# 输出示例:
# 找到匹配地址!
# 地址: TTestABCDEFGHIJKLMNOPQRSTUVWXYZ123
# 私钥: 1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
# 公钥: 04abcdef...
# 迭代次数: 195,432
# 耗时: 78.2 秒
```

### 2. 使用GPU加速

```bash
# 启用GPU加速
./TronVanity --gpu TRich

# 指定GPU设备
./TronVanity --gpu --device 0 TLucky
```

### 3. 多模式搜索

```bash
# 同时搜索多个模式
./TronVanity T888 T666 TGood

# 找到任意一个匹配就停止
./TronVanity --stop-first T123 T456 T789
```

## 高级功能

### 4. 通配符模式

```bash
# 使用通配符 (? = 单个字符, * = 任意字符)
./TronVanity "T?ron*"     # T + 任意字符 + ron + 任意字符
./TronVanity "T*888"      # T + 任意字符 + 888
./TronVanity "T??88"      # T + 两个任意字符 + 88
```

### 5. 大小写不敏感搜索

```bash
# 忽略大小写
./TronVanity --case-insensitive ttest

# 这将匹配: TTest, TTEST, ttest, TtEsT 等
```

### 6. 输出到文件

```bash
# 保存结果到文件
./TronVanity --output results.txt TMyWallet

# 指定输出格式
./TronVanity --output results.json --format json TRich
```

### 7. 性能调优

```bash
# 指定CPU线程数
./TronVanity --threads 8 TTest

# 指定GPU网格大小
./TronVanity --gpu --grid-size 256,128 TLucky

# 调整批处理大小
./TronVanity --batch-size 10000 T888
```

## 配置文件使用

### 8. 使用配置文件

创建配置文件 `config.txt`:
```
# 搜索模式
TMyWallet
TRich
TLucky

# GPU设置
use_gpu=true
threads_per_block=256

# 输出设置
output_file=my_results.txt
case_insensitive=false
```

运行:
```bash
./TronVanity --config config.txt
```

## 实际应用场景

### 9. 个人钱包地址

```bash
# 生成个人标识的钱包地址
./TronVanity --gpu --output my_wallet.txt TJohn

# 生成生日相关地址
./TronVanity T1990 T0315  # 1990年3月15日

# 生成幸运数字地址
./TronVanity T888 T666 T168
```

### 10. 商业用途

```bash
# 为公司生成品牌地址
./TronVanity --gpu TCompany TBrand

# 为项目生成专用地址
./TronVanity TNFT TDefi TGame

# 生成易记的地址
./TronVanity TEasy TSimple TGood
```

### 11. 批量生成

```bash
# 生成多个地址（找到一个后继续）
./TronVanity --continue --max-results 10 TTest

# 为团队成员生成地址
./TronVanity --continue TAlice TBob TCharlie
```

## 监控和统计

### 12. 实时监控

```bash
# 显示详细进度信息
./TronVanity --verbose --progress TTest

# 输出示例:
# [INFO] 搜索模式: TTest
# [INFO] 预估难度: 195,112
# [INFO] 使用GPU: NVIDIA RTX 3080
# [PROGRESS] 速度: 2,500,000 keys/s | 已尝试: 1,234,567 | 概率: 0.63% | 预估剩余: 45秒
```

### 13. 基准测试

```bash
# 运行性能基准测试
./TronVanity --benchmark --duration 60

# 输出示例:
# GPU性能测试 (60秒):
# 平均速度: 2,456,789 keys/s
# 峰值速度: 2,678,901 keys/s
# GPU利用率: 98.5%
# 内存使用: 2.1GB / 8GB
```

## 安全最佳实践

### 14. 安全生成

```bash
# 在离线环境中生成
./TronVanity --offline --secure TMySecure

# 验证生成的密钥对
./TronVanity --verify-only --private-key 1234...abcd

# 使用自定义熵源
./TronVanity --entropy-file /dev/urandom TSecure
```

### 15. 结果验证

```bash
# 验证地址有效性
./TronVanity --validate-address TTestABCDEFGHIJKLMNOPQRSTUVWXYZ123

# 验证私钥和地址匹配
./TronVanity --verify-keypair \
  --private-key 1234567890abcdef... \
  --address TTestABCDEFGHIJKLMNOPQRSTUVWXYZ123
```

## 故障排除示例

### 16. 常见问题解决

```bash
# GPU内存不足时使用CPU
./TronVanity --cpu-only TTest

# 降低GPU内存使用
./TronVanity --gpu --grid-size 128,64 --batch-size 1000 TTest

# 调试模式运行
./TronVanity --debug --verbose TTest
```

### 17. 性能问题诊断

```bash
# 检查系统性能
./TronVanity --system-info

# 输出示例:
# CPU: Intel i7-10700K @ 3.8GHz (8 cores)
# RAM: 32GB DDR4-3200
# GPU: NVIDIA RTX 3080 (8704 CUDA cores, 10GB VRAM)
# CUDA版本: 11.2
# 驱动版本: 460.89
```

## 脚本自动化

### 18. 批处理脚本

创建 `generate_wallets.sh`:
```bash
#!/bin/bash

# 为团队生成钱包地址
NAMES=("Alice" "Bob" "Charlie" "David" "Eve")

for name in "${NAMES[@]}"; do
    echo "为 $name 生成地址..."
    ./TronVanity --gpu --output "${name}_wallet.txt" "T${name}"
    
    if [ $? -eq 0 ]; then
        echo "✓ $name 的地址生成完成"
    else
        echo "✗ $name 的地址生成失败"
    fi
done

echo "所有地址生成完成！"
```

### 19. Python集成示例

```python
import subprocess
import json

def generate_vanity_address(pattern, use_gpu=True):
    """生成TRON靓号地址"""
    cmd = ["./TronVanity", "--format", "json"]
    
    if use_gpu:
        cmd.append("--gpu")
    
    cmd.append(pattern)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            return json.loads(result.stdout)
        else:
            print(f"错误: {result.stderr}")
            return None
    except Exception as e:
        print(f"执行失败: {e}")
        return None

# 使用示例
address_info = generate_vanity_address("TTest")
if address_info:
    print(f"地址: {address_info['address']}")
    print(f"私钥: {address_info['private_key']}")
```

## 性能优化建议

### 20. 最佳实践

```bash
# 对于短模式（4-5字符），使用GPU
./TronVanity --gpu T1234

# 对于长模式（6+字符），考虑分布式
./TronVanity --distributed --nodes node1,node2,node3 T123456

# 在服务器上长时间运行
nohup ./TronVanity --gpu --continue --max-results 100 TTest > output.log 2>&1 &

# 使用screen保持会话
screen -S vanity
./TronVanity --gpu TMyLongPattern
# Ctrl+A, D 分离会话
```

### 21. 资源管理

```bash
# 限制CPU使用率
nice -n 10 ./TronVanity --cpu-only TTest

# 限制内存使用
./TronVanity --gpu --max-memory 4GB TTest

# 设置运行时间限制
timeout 3600 ./TronVanity --gpu TTest  # 1小时后停止
```

## 结果分析

### 22. 统计分析

```bash
# 生成统计报告
./TronVanity --stats --pattern TTest --duration 300

# 输出示例:
# 统计报告 (5分钟):
# 总尝试次数: 750,000,000
# 平均速度: 2,500,000 keys/s
# 理论概率: 0.38%
# 实际找到: 0 个
# 预估完成时间: 2.1 小时
```

这些示例涵盖了从基本使用到高级功能的各种场景，帮助用户充分利用TRON靓号地址生成器的功能。
