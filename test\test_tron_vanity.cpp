#include "../src/TronVanity.h"
#include "../src/Hash.h"
#include "../src/PatternMatcher.h"
#include <iostream>
#include <cassert>
#include <chrono>

// 测试工具函数
void testTronUtils() {
    std::cout << "测试 TronUtils..." << std::endl;
    
    // 测试私钥生成
    auto privateKey = TronUtils::generatePrivateKey();
    assert(privateKey.size() == 32);
    std::cout << "✓ 私钥生成测试通过" << std::endl;
    
    // 测试公钥计算
    try {
        auto publicKey = TronUtils::privateKeyToPublicKey(privateKey);
        assert(publicKey.size() == 64);
        std::cout << "✓ 公钥计算测试通过" << std::endl;
        
        // 测试地址生成
        std::string address = TronUtils::publicKeyToAddress(publicKey);
        assert(address.length() == 34);
        assert(address[0] == 'T');
        std::cout << "✓ 地址生成测试通过: " << address << std::endl;
        
        // 测试地址验证
        assert(TronUtils::isValidTronAddress(address));
        std::cout << "✓ 地址验证测试通过" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "✗ 公钥/地址测试失败: " << e.what() << std::endl;
    }
    
    // 测试十六进制转换
    std::string hex = TronUtils::bytesToHex(privateKey);
    assert(hex.length() == 64);
    
    auto bytesBack = TronUtils::hexToBytes(hex);
    assert(bytesBack == privateKey);
    std::cout << "✓ 十六进制转换测试通过" << std::endl;
    
    // 测试WIF格式
    std::string wif = TronUtils::privateKeyToWIF(privateKey);
    assert(!wif.empty());
    std::cout << "✓ WIF格式测试通过: " << wif << std::endl;
}

// 测试哈希函数
void testHashFunctions() {
    std::cout << "\n测试哈希函数..." << std::endl;
    
    // 测试Keccak256
    std::string testData = "Hello, TRON!";
    std::vector<uint8_t> data(testData.begin(), testData.end());
    
    auto keccakHash = Keccak256::hash(data);
    assert(keccakHash.size() == 32);
    std::cout << "✓ Keccak256测试通过" << std::endl;
    
    // 测试SHA256
    auto sha256Hash = SHA256::hash(data);
    assert(sha256Hash.size() == 32);
    std::cout << "✓ SHA256测试通过" << std::endl;
    
    // 测试双重SHA256
    auto doubleSha256 = SHA256::doubleHash(data);
    assert(doubleSha256.size() == 32);
    std::cout << "✓ 双重SHA256测试通过" << std::endl;
}

// 测试Base58编码
void testBase58() {
    std::cout << "\n测试Base58编码..." << std::endl;
    
    // 测试基本编码/解码
    std::vector<uint8_t> testData = {0x00, 0x01, 0x02, 0x03, 0x04, 0x05};
    std::string encoded = Base58::encode(testData);
    auto decoded = Base58::decode(encoded);
    
    assert(decoded == testData);
    std::cout << "✓ Base58编码/解码测试通过" << std::endl;
    
    // 测试Base58Check
    std::string encodedCheck = Base58::encodeCheck(testData);
    auto decodedCheck = Base58::decodeCheck(encodedCheck);
    
    assert(decodedCheck == testData);
    std::cout << "✓ Base58Check编码/解码测试通过" << std::endl;
    
    // 测试验证
    assert(Base58::isValidCheck(encodedCheck));
    std::cout << "✓ Base58Check验证测试通过" << std::endl;
}

// 测试模式匹配
void testPatternMatcher() {
    std::cout << "\n测试模式匹配..." << std::endl;
    
    auto matcher = MatcherFactory::createMatcher(MatcherFactory::SIMPLE_MATCHER);
    assert(matcher != nullptr);
    
    // 测试前缀匹配
    assert(matcher->addPattern("TTest", PatternType::PREFIX, false));
    assert(matcher->isMatch("TTestABCDEFGHIJKLMNOPQRSTUVWXYZ123"));
    assert(!matcher->isMatch("TAnotherAddress"));
    std::cout << "✓ 前缀匹配测试通过" << std::endl;
    
    // 测试大小写不敏感
    matcher->clearPatterns();
    assert(matcher->addPattern("ttest", PatternType::PREFIX, true));
    assert(matcher->isMatch("TTestABCDEFGHIJKLMNOPQRSTUVWXYZ123"));
    std::cout << "✓ 大小写不敏感测试通过" << std::endl;
    
    // 测试通配符
    matcher->clearPatterns();
    assert(matcher->addPattern("T?est*", PatternType::WILDCARD, false));
    assert(matcher->isMatch("TTestABCDEFGHIJKLMNOPQRSTUVWXYZ123"));
    assert(matcher->isMatch("TAestXYZ"));
    assert(!matcher->isMatch("TBBestXYZ"));
    std::cout << "✓ 通配符匹配测试通过" << std::endl;
    
    // 测试难度计算
    Pattern pattern("TTest", PatternType::PREFIX, false);
    double difficulty = matcher->calculateDifficulty(pattern);
    assert(difficulty > 1.0);
    std::cout << "✓ 难度计算测试通过: " << difficulty << std::endl;
}

// 测试完整的地址生成流程
void testCompleteFlow() {
    std::cout << "\n测试完整地址生成流程..." << std::endl;
    
    int testCount = 10;
    int validAddresses = 0;
    
    for (int i = 0; i < testCount; ++i) {
        try {
            // 生成私钥
            auto privateKey = TronUtils::generatePrivateKey();
            
            // 计算公钥
            auto publicKey = TronUtils::privateKeyToPublicKey(privateKey);
            
            // 生成地址
            std::string address = TronUtils::publicKeyToAddress(publicKey);
            
            // 验证地址
            if (TronUtils::isValidTronAddress(address)) {
                validAddresses++;
            }
            
            std::cout << "地址 " << (i+1) << ": " << address << std::endl;
            
        } catch (const std::exception& e) {
            std::cout << "生成地址 " << (i+1) << " 失败: " << e.what() << std::endl;
        }
    }
    
    std::cout << "✓ 完整流程测试: " << validAddresses << "/" << testCount << " 个有效地址" << std::endl;
    assert(validAddresses == testCount);
}

// 性能测试
void performanceTest() {
    std::cout << "\n性能测试..." << std::endl;
    
    const int iterations = 1000;
    auto startTime = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < iterations; ++i) {
        auto privateKey = TronUtils::generatePrivateKey();
        auto publicKey = TronUtils::privateKeyToPublicKey(privateKey);
        auto address = TronUtils::publicKeyToAddress(publicKey);
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    double keysPerSecond = (iterations * 1000.0) / duration.count();
    std::cout << "✓ 性能测试: " << keysPerSecond << " keys/s" << std::endl;
}

// 测试搜索配置
void testSearchConfig() {
    std::cout << "\n测试搜索配置..." << std::endl;
    
    TronVanitySearch search;
    SearchConfig config;
    
    // 基本配置
    config.patterns = {"TTest"};
    config.caseInsensitive = false;
    config.stopOnFirst = true;
    config.useGPU = false;  // 测试时使用CPU
    config.cpuThreads = 1;
    
    assert(search.configure(config));
    std::cout << "✓ 搜索配置测试通过" << std::endl;
    
    // 测试无效模式
    config.patterns = {"InvalidPattern!"};
    assert(!search.configure(config));
    std::cout << "✓ 无效模式检测测试通过" << std::endl;
}

int main() {
    std::cout << "TRON 靓号地址生成器测试" << std::endl;
    std::cout << "========================" << std::endl;
    
    try {
        testTronUtils();
        testHashFunctions();
        testBase58();
        testPatternMatcher();
        testCompleteFlow();
        testSearchConfig();
        performanceTest();
        
        std::cout << "\n🎉 所有测试通过！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "\n❌ 测试失败: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "\n❌ 未知错误" << std::endl;
        return 1;
    }
    
    return 0;
}
