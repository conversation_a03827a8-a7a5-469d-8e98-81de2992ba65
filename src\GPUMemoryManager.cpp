#ifdef USE_CUDA

#include "GPUEngine.h"
#include <iostream>
#include <stdexcept>

GPUMemoryManager::GPUMemoryManager(int deviceId) 
    : m_deviceId(deviceId), m_totalMemory(0), m_availableMemory(0) {
    
    // 设置设备
    cudaSetDevice(m_deviceId);
    
    // 获取内存信息
    size_t free, total;
    cudaError_t error = cudaMemGetInfo(&free, &total);
    if (error == cudaSuccess) {
        m_totalMemory = total;
        m_availableMemory = free;
    }
    
    std::cout << "GPU内存管理器初始化 - 设备 " << m_deviceId << std::endl;
    std::cout << "总内存: " << (m_totalMemory / 1024 / 1024) << " MB" << std::endl;
    std::cout << "可用内存: " << (m_availableMemory / 1024 / 1024) << " MB" << std::endl;
}

GPUMemoryManager::~GPUMemoryManager() {
    // 析构函数中不需要特殊清理，CUDA会自动管理内存
}

void* GPUMemoryManager::allocateDevice(size_t size) {
    if (size == 0) {
        return nullptr;
    }
    
    void* ptr = nullptr;
    cudaError_t error = cudaMalloc(&ptr, size);
    
    if (error != cudaSuccess) {
        std::cerr << "GPU内存分配失败: " << cudaGetErrorString(error) 
                  << " (大小: " << size << " 字节)" << std::endl;
        return nullptr;
    }
    
    // 更新可用内存估算
    m_availableMemory = (m_availableMemory > size) ? (m_availableMemory - size) : 0;
    
    return ptr;
}

void* GPUMemoryManager::allocateHost(size_t size) {
    if (size == 0) {
        return nullptr;
    }
    
    void* ptr = malloc(size);
    if (!ptr) {
        std::cerr << "主机内存分配失败 (大小: " << size << " 字节)" << std::endl;
    }
    
    return ptr;
}

void* GPUMemoryManager::allocateHostPinned(size_t size) {
    if (size == 0) {
        return nullptr;
    }
    
    void* ptr = nullptr;
    cudaError_t error = cudaMallocHost(&ptr, size);
    
    if (error != cudaSuccess) {
        std::cerr << "固定主机内存分配失败: " << cudaGetErrorString(error) 
                  << " (大小: " << size << " 字节)" << std::endl;
        return nullptr;
    }
    
    return ptr;
}

void GPUMemoryManager::freeDevice(void* ptr) {
    if (ptr) {
        cudaError_t error = cudaFree(ptr);
        if (error != cudaSuccess) {
            std::cerr << "GPU内存释放失败: " << cudaGetErrorString(error) << std::endl;
        }
    }
}

void GPUMemoryManager::freeHost(void* ptr) {
    if (ptr) {
        free(ptr);
    }
}

void GPUMemoryManager::freeHostPinned(void* ptr) {
    if (ptr) {
        cudaError_t error = cudaFreeHost(ptr);
        if (error != cudaSuccess) {
            std::cerr << "固定主机内存释放失败: " << cudaGetErrorString(error) << std::endl;
        }
    }
}

bool GPUMemoryManager::copyHostToDevice(void* dst, const void* src, size_t size) {
    if (!dst || !src || size == 0) {
        return false;
    }
    
    cudaError_t error = cudaMemcpy(dst, src, size, cudaMemcpyHostToDevice);
    if (error != cudaSuccess) {
        std::cerr << "主机到设备内存拷贝失败: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    return true;
}

bool GPUMemoryManager::copyDeviceToHost(void* dst, const void* src, size_t size) {
    if (!dst || !src || size == 0) {
        return false;
    }
    
    cudaError_t error = cudaMemcpy(dst, src, size, cudaMemcpyDeviceToHost);
    if (error != cudaSuccess) {
        std::cerr << "设备到主机内存拷贝失败: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    return true;
}

bool GPUMemoryManager::copyDeviceToDevice(void* dst, const void* src, size_t size) {
    if (!dst || !src || size == 0) {
        return false;
    }
    
    cudaError_t error = cudaMemcpy(dst, src, size, cudaMemcpyDeviceToDevice);
    if (error != cudaSuccess) {
        std::cerr << "设备到设备内存拷贝失败: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    return true;
}

bool GPUMemoryManager::copyHostToDeviceAsync(void* dst, const void* src, size_t size, void* stream) {
    if (!dst || !src || size == 0) {
        return false;
    }
    
    cudaStream_t cudaStream = static_cast<cudaStream_t>(stream);
    cudaError_t error = cudaMemcpyAsync(dst, src, size, cudaMemcpyHostToDevice, cudaStream);
    if (error != cudaSuccess) {
        std::cerr << "异步主机到设备内存拷贝失败: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    return true;
}

bool GPUMemoryManager::copyDeviceToHostAsync(void* dst, const void* src, size_t size, void* stream) {
    if (!dst || !src || size == 0) {
        return false;
    }
    
    cudaStream_t cudaStream = static_cast<cudaStream_t>(stream);
    cudaError_t error = cudaMemcpyAsync(dst, src, size, cudaMemcpyDeviceToHost, cudaStream);
    if (error != cudaSuccess) {
        std::cerr << "异步设备到主机内存拷贝失败: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    return true;
}

size_t GPUMemoryManager::getAvailableMemory() const {
    size_t free, total;
    cudaError_t error = cudaMemGetInfo(&free, &total);
    if (error == cudaSuccess) {
        return free;
    }
    return m_availableMemory;
}

size_t GPUMemoryManager::getTotalMemory() const {
    return m_totalMemory;
}

#endif // USE_CUDA
