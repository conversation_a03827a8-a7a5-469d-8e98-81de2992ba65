#include "Hash.h"
#include <algorithm>
#include <stdexcept>

// Base58字符表 (Bitcoin/TRON使用)
const char Base58::ALPHABET[58] = 
    "**********************************************************";

// Base58编码
std::string Base58::encode(const std::vector<uint8_t>& data) {
    if (data.empty()) {
        return "";
    }
    
    // 计算前导零的数量
    int leadingZeros = 0;
    for (uint8_t byte : data) {
        if (byte == 0) {
            leadingZeros++;
        } else {
            break;
        }
    }
    
    // 将数据转换为大整数 (使用256进制)
    std::vector<uint8_t> digits(data.begin() + leadingZeros, data.end());
    
    // 转换为Base58
    std::string result;
    while (!digits.empty()) {
        int remainder = 0;
        
        // 除以58
        for (int i = 0; i < digits.size(); ++i) {
            int temp = remainder * 256 + digits[i];
            digits[i] = temp / 58;
            remainder = temp % 58;
        }
        
        // 添加余数对应的字符
        result = ALPHABET[remainder] + result;
        
        // 移除前导零
        while (!digits.empty() && digits[0] == 0) {
            digits.erase(digits.begin());
        }
    }
    
    // 添加前导零对应的'1'字符
    for (int i = 0; i < leadingZeros; ++i) {
        result = '1' + result;
    }
    
    return result;
}

// Base58解码
std::vector<uint8_t> Base58::decode(const std::string& encoded) {
    if (encoded.empty()) {
        return {};
    }
    
    // 计算前导'1'的数量
    int leadingOnes = 0;
    for (char c : encoded) {
        if (c == '1') {
            leadingOnes++;
        } else {
            break;
        }
    }
    
    // 创建字符到索引的映射
    int charMap[256];
    std::fill(charMap, charMap + 256, -1);
    for (int i = 0; i < 58; ++i) {
        charMap[static_cast<uint8_t>(ALPHABET[i])] = i;
    }
    
    // 将Base58字符串转换为大整数
    std::vector<uint8_t> digits;
    for (int i = leadingOnes; i < encoded.length(); ++i) {
        int value = charMap[static_cast<uint8_t>(encoded[i])];
        if (value == -1) {
            throw std::invalid_argument("Invalid Base58 character");
        }
        
        // 乘以58并加上当前值
        int carry = value;
        for (int j = digits.size() - 1; j >= 0; --j) {
            carry += digits[j] * 58;
            digits[j] = carry & 0xFF;
            carry >>= 8;
        }
        
        while (carry > 0) {
            digits.insert(digits.begin(), carry & 0xFF);
            carry >>= 8;
        }
    }
    
    // 添加前导零
    std::vector<uint8_t> result(leadingOnes, 0);
    result.insert(result.end(), digits.begin(), digits.end());
    
    return result;
}

// Base58Check编码
std::string Base58::encodeCheck(const std::vector<uint8_t>& data) {
    // 计算校验和
    std::vector<uint8_t> checksum = calculateChecksum(data);
    
    // 拼接数据和校验和
    std::vector<uint8_t> dataWithChecksum = data;
    dataWithChecksum.insert(dataWithChecksum.end(), checksum.begin(), checksum.end());
    
    // Base58编码
    return encode(dataWithChecksum);
}

// Base58Check解码
std::vector<uint8_t> Base58::decodeCheck(const std::string& encoded) {
    // Base58解码
    std::vector<uint8_t> decoded = decode(encoded);
    
    if (decoded.size() < 4) {
        throw std::invalid_argument("Invalid Base58Check data: too short");
    }
    
    // 分离数据和校验和
    std::vector<uint8_t> data(decoded.begin(), decoded.end() - 4);
    std::vector<uint8_t> checksum(decoded.end() - 4, decoded.end());
    
    // 验证校验和
    std::vector<uint8_t> expectedChecksum = calculateChecksum(data);
    if (checksum != expectedChecksum) {
        throw std::invalid_argument("Invalid Base58Check checksum");
    }
    
    return data;
}

// 验证Base58Check格式
bool Base58::isValidCheck(const std::string& encoded) {
    try {
        decodeCheck(encoded);
        return true;
    } catch (...) {
        return false;
    }
}

// 计算校验和 (双重SHA256的前4字节)
std::vector<uint8_t> Base58::calculateChecksum(const std::vector<uint8_t>& data) {
    std::vector<uint8_t> hash = SHA256::doubleHash(data);
    return std::vector<uint8_t>(hash.begin(), hash.begin() + 4);
}
