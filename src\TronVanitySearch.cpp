#include "TronVanity.h"
#include "GPUEngine.h"
#include "PatternMatcher.h"
#include <iostream>
#include <fstream>
#include <thread>
#include <chrono>
#include <random>

TronVanitySearch::TronVanitySearch() {
    m_stats.startTime = TronUtils::getCurrentTime();
}

TronVanitySearch::~TronVanitySearch() {
    stopSearch();
}

bool TronVanitySearch::configure(const SearchConfig& config) {
    if (config.patterns.empty()) {
        std::cerr << "错误: 未指定搜索模式" << std::endl;
        return false;
    }
    
    m_config = config;
    
    // 创建模式匹配器
    auto matcherType = MatcherFactory::getBestMatcher(
        static_cast<int>(config.patterns.size()), config.useGPU);
    m_patternMatcher = MatcherFactory::createMatcher(matcherType);
    
    if (!m_patternMatcher) {
        std::cerr << "错误: 创建模式匹配器失败" << std::endl;
        return false;
    }
    
    // 添加搜索模式
    for (const auto& patternStr : config.patterns) {
        PatternType type = PatternUtils::detectPatternType(patternStr);
        
        if (!m_patternMatcher->addPattern(patternStr, type, config.caseInsensitive)) {
            std::cerr << "错误: 无效的搜索模式: " << patternStr << std::endl;
            return false;
        }
    }
    
    // 优化模式匹配器
    m_patternMatcher->optimize();
    
    // 创建GPU引擎（如果需要）
    if (config.useGPU) {
        m_gpuEngine = GPUEngineFactory::createEngine();
        if (!m_gpuEngine) {
            std::cerr << "警告: GPU引擎创建失败，回退到CPU模式" << std::endl;
            m_config.useGPU = false;
        } else if (!m_gpuEngine->initialize()) {
            std::cerr << "警告: GPU引擎初始化失败，回退到CPU模式" << std::endl;
            m_gpuEngine.reset();
            m_config.useGPU = false;
        }
    }
    
    // 设置CPU线程数
    if (m_config.cpuThreads == 0) {
        m_config.cpuThreads = std::thread::hardware_concurrency();
        if (m_config.cpuThreads == 0) {
            m_config.cpuThreads = 4;  // 默认4线程
        }
    }
    
    // 如果使用GPU，减少CPU线程数
    if (m_config.useGPU && m_config.cpuThreads > 2) {
        m_config.cpuThreads = 2;  // 保留2个CPU线程处理GPU交互
    }
    
    // 计算难度和预估时间
    calculateProbability();
    
    std::cout << "搜索配置完成:" << std::endl;
    std::cout << "  模式数量: " << m_patternMatcher->getPatternCount() << std::endl;
    std::cout << "  总难度: " << m_patternMatcher->getTotalDifficulty() << std::endl;
    std::cout << "  使用GPU: " << (m_config.useGPU ? "是" : "否") << std::endl;
    std::cout << "  CPU线程: " << m_config.cpuThreads << std::endl;
    
    return true;
}

bool TronVanitySearch::startSearch() {
    if (m_running) {
        return false;
    }
    
    if (!m_patternMatcher) {
        std::cerr << "错误: 未配置搜索参数" << std::endl;
        return false;
    }
    
    m_running = true;
    m_stats.startTime = TronUtils::getCurrentTime();
    
    // 启动GPU搜索（如果可用）
    if (m_config.useGPU && m_gpuEngine) {
        if (!m_gpuEngine->startSearch(m_config.patterns)) {
            std::cerr << "警告: GPU搜索启动失败" << std::endl;
        }
    }
    
    // 启动CPU搜索线程
    for (int i = 0; i < m_config.cpuThreads; ++i) {
        m_cpuThreads.emplace_back(&TronVanitySearch::cpuWorker, this, i);
    }
    
    // 启动统计更新线程
    std::thread statsThread(&TronVanitySearch::updateStats, this);
    statsThread.detach();
    
    return true;
}

void TronVanitySearch::stopSearch() {
    m_running = false;
    
    // 停止GPU搜索
    if (m_gpuEngine) {
        m_gpuEngine->stopSearch();
    }
    
    // 等待CPU线程结束
    for (auto& thread : m_cpuThreads) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    m_cpuThreads.clear();
}

std::vector<SearchResult> TronVanitySearch::getResults() const {
    std::lock_guard<std::mutex> lock(m_resultsMutex);
    return m_results;
}

PerformanceStats TronVanitySearch::getStats() const {
    return m_stats;
}

void TronVanitySearch::setResultCallback(std::function<void(const SearchResult&)> callback) {
    m_resultCallback = callback;
}

void TronVanitySearch::setProgressCallback(std::function<void(const PerformanceStats&)> callback) {
    m_progressCallback = callback;
}

void TronVanitySearch::cpuWorker(int threadId) {
    std::random_device rd;
    std::mt19937_64 gen(rd() ^ threadId);
    
    uint64_t localIterations = 0;
    const uint64_t batchSize = 1000;  // 每批处理1000个密钥
    
    while (m_running) {
        for (uint64_t i = 0; i < batchSize && m_running; ++i) {
            try {
                // 生成随机私钥
                std::vector<uint8_t> privateKey = TronUtils::generatePrivateKey();
                
                // 计算公钥
                std::vector<uint8_t> publicKey = TronUtils::privateKeyToPublicKey(privateKey);
                
                // 生成地址
                std::string address = TronUtils::publicKeyToAddress(publicKey);
                
                // 检查是否匹配模式
                if (m_patternMatcher->isMatch(address)) {
                    // 找到匹配！
                    SearchResult result;
                    result.address = address;
                    result.privateKey = TronUtils::bytesToHex(privateKey);
                    result.privateKeyWIF = TronUtils::privateKeyToWIF(privateKey);
                    result.publicKey = TronUtils::bytesToHex(publicKey);
                    result.iterations = localIterations + i + 1;
                    result.elapsedTime = TronUtils::getCurrentTime() - m_stats.startTime;
                    
                    saveResult(result);
                    
                    // 如果设置为找到第一个就停止
                    if (m_config.stopOnFirst) {
                        m_running = false;
                        break;
                    }
                }
                
            } catch (const std::exception& e) {
                std::cerr << "CPU工作线程 " << threadId << " 错误: " << e.what() << std::endl;
            }
        }
        
        localIterations += batchSize;
        m_stats.cpuKeys += batchSize;
        m_stats.totalKeys += batchSize;
        
        // 避免过度占用CPU
        std::this_thread::sleep_for(std::chrono::microseconds(100));
    }
}

void TronVanitySearch::updateStats() {
    auto lastUpdate = std::chrono::steady_clock::now();
    uint64_t lastTotalKeys = 0;
    
    while (m_running) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - lastUpdate);
        
        if (elapsed.count() > 0) {
            uint64_t currentTotalKeys = m_stats.totalKeys;
            uint64_t keysDiff = currentTotalKeys - lastTotalKeys;
            
            m_stats.keysPerSecond = keysDiff / elapsed.count();
            
            // 添加GPU统计
            if (m_gpuEngine) {
                uint64_t gpuKeys = m_gpuEngine->getIterationCount();
                m_stats.gpuKeys = gpuKeys;
                m_stats.totalKeys = m_stats.cpuKeys + gpuKeys;
            }
            
            // 更新概率和预估时间
            calculateProbability();
            
            // 调用进度回调
            if (m_progressCallback) {
                m_progressCallback(m_stats);
            }
            
            lastUpdate = now;
            lastTotalKeys = currentTotalKeys;
        }
    }
}

void TronVanitySearch::calculateProbability() {
    double difficulty = m_patternMatcher->getTotalDifficulty();
    uint64_t attempts = m_stats.totalKeys;
    
    if (difficulty > 0) {
        // 计算成功概率
        m_stats.probability = PatternUtils::calculateSuccessProbability(attempts, difficulty) * 100.0;
        
        // 计算预估剩余时间（50%概率）
        if (m_stats.keysPerSecond > 0) {
            uint64_t expectedAttempts = static_cast<uint64_t>(difficulty * 0.693);  // ln(2)
            if (attempts < expectedAttempts) {
                uint64_t remainingAttempts = expectedAttempts - attempts;
                m_stats.estimatedTime = static_cast<double>(remainingAttempts) / m_stats.keysPerSecond;
            } else {
                m_stats.estimatedTime = 0.0;
            }
        }
    }
}

bool TronVanitySearch::isPatternMatch(const std::string& address) {
    return m_patternMatcher->isMatch(address);
}

void TronVanitySearch::saveResult(const SearchResult& result) {
    {
        std::lock_guard<std::mutex> lock(m_resultsMutex);
        m_results.push_back(result);
        m_stats.foundCount++;
    }
    
    // 调用结果回调
    if (m_resultCallback) {
        m_resultCallback(result);
    }
    
    // 保存到文件（如果指定了输出文件）
    if (!m_config.outputFile.empty()) {
        std::ofstream file(m_config.outputFile, std::ios::app);
        if (file.is_open()) {
            file << "地址: " << result.address << std::endl;
            file << "私钥 (HEX): " << result.privateKey << std::endl;
            file << "私钥 (WIF): " << result.privateKeyWIF << std::endl;
            file << "公钥: " << result.publicKey << std::endl;
            file << "迭代次数: " << result.iterations << std::endl;
            file << "耗时: " << result.elapsedTime << " 秒" << std::endl;
            file << "---" << std::endl;
            file.close();
        }
    }
}
