#include "TronVanity.h"
#include "GPUEngine.h"
#include "PatternMatcher.h"
#include <iostream>
#include <iomanip>
#include <thread>
#include <chrono>
#include <signal.h>

// 全局变量用于信号处理
std::unique_ptr<TronVanitySearch> g_search;
std::atomic<bool> g_running{true};

// 信号处理函数
void signalHandler(int signal) {
    std::cout << "\n收到停止信号，正在安全退出..." << std::endl;
    g_running = false;
    if (g_search) {
        g_search->stopSearch();
    }
}

// 显示帮助信息
void showHelp() {
    std::cout << "TRON 靓号地址生成器 v" << TRON_VANITY_VERSION << std::endl;
    std::cout << "使用方法: TronVanity [选项] <模式>" << std::endl;
    std::cout << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  -h, --help              显示帮助信息" << std::endl;
    std::cout << "  -v, --version           显示版本信息" << std::endl;
    std::cout << "  -gpu                    启用GPU计算" << std::endl;
    std::cout << "  -cpu                    仅使用CPU计算" << std::endl;
    std::cout << "  -gpuId <id1,id2,...>    指定GPU设备ID" << std::endl;
    std::cout << "  -t <threads>            CPU线程数" << std::endl;
    std::cout << "  -c, --case-insensitive  大小写不敏感搜索" << std::endl;
    std::cout << "  -stop                   找到第一个匹配就停止" << std::endl;
    std::cout << "  -continue               继续搜索直到手动停止" << std::endl;
    std::cout << "  -o <file>               输出结果到文件" << std::endl;
    std::cout << "  -s <seed>               指定种子短语" << std::endl;
    std::cout << "  -g <x,y>                指定GPU网格大小" << std::endl;
    std::cout << "  --verbose               详细输出模式" << std::endl;
    std::cout << "  -l, --list-devices      列出可用的GPU设备" << std::endl;
    std::cout << std::endl;
    std::cout << "模式格式:" << std::endl;
    std::cout << "  TMyPrefix               前缀匹配" << std::endl;
    std::cout << "  *MySuffix               后缀匹配" << std::endl;
    std::cout << "  T?ron*                  通配符匹配 (? = 单个字符, * = 任意字符)" << std::endl;
    std::cout << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  TronVanity -gpu -stop TMyPrefix" << std::endl;
    std::cout << "  TronVanity -gpu -c -o results.txt T?ron*" << std::endl;
    std::cout << "  TronVanity -gpu -gpuId 0,1 -t 4 TMyPrefix" << std::endl;
    std::cout << std::endl;
    std::cout << "安全提醒:" << std::endl;
    std::cout << "  1. 生成的私钥和地址必须进行验证" << std::endl;
    std::cout << "  2. 建议对生成的地址进行多签处理" << std::endl;
    std::cout << "  3. 妥善保管私钥，避免泄露" << std::endl;
}

// 显示版本信息
void showVersion() {
    std::cout << "TRON 靓号地址生成器 v" << TRON_VANITY_VERSION << std::endl;
    std::cout << "基于GPU加速的高性能TRON地址生成工具" << std::endl;
    std::cout << "支持CUDA和OpenCL" << std::endl;
}

// 列出GPU设备
void listGPUDevices() {
    std::cout << "检测GPU设备..." << std::endl;
    
    auto engine = GPUEngineFactory::createEngine();
    if (!engine) {
        std::cout << "未检测到可用的GPU设备" << std::endl;
        return;
    }
    
    if (!engine->initialize()) {
        std::cout << "GPU引擎初始化失败" << std::endl;
        return;
    }
    
    auto devices = engine->getDeviceList();
    if (devices.empty()) {
        std::cout << "未找到可用的GPU设备" << std::endl;
        return;
    }
    
    std::cout << "可用的GPU设备:" << std::endl;
    for (const auto& device : devices) {
        std::cout << "  设备 " << device.deviceId << ": " << device.name << std::endl;
        std::cout << "    全局内存: " << (device.globalMemory / 1024 / 1024) << " MB" << std::endl;
        std::cout << "    多处理器: " << device.multiProcessors << std::endl;
        std::cout << "    计算能力: " << device.computeCapabilityMajor << "." 
                  << device.computeCapabilityMinor << std::endl;
        std::cout << std::endl;
    }
}

// 解析命令行参数
SearchConfig parseArguments(int argc, char* argv[]) {
    SearchConfig config;
    
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            showHelp();
            exit(0);
        } else if (arg == "-v" || arg == "--version") {
            showVersion();
            exit(0);
        } else if (arg == "-l" || arg == "--list-devices") {
            listGPUDevices();
            exit(0);
        } else if (arg == "-gpu") {
            config.useGPU = true;
        } else if (arg == "-cpu") {
            config.useGPU = false;
        } else if (arg == "-c" || arg == "--case-insensitive") {
            config.caseInsensitive = true;
        } else if (arg == "-stop") {
            config.stopOnFirst = true;
        } else if (arg == "-continue") {
            config.stopOnFirst = false;
        } else if (arg == "--verbose") {
            config.verbose = true;
        } else if (arg == "-gpuId" && i + 1 < argc) {
            std::string gpuIds = argv[++i];
            // 解析GPU ID列表
            std::stringstream ss(gpuIds);
            std::string id;
            while (std::getline(ss, id, ',')) {
                config.gpuIds.push_back(std::stoi(id));
            }
        } else if (arg == "-t" && i + 1 < argc) {
            config.cpuThreads = std::stoi(argv[++i]);
        } else if (arg == "-o" && i + 1 < argc) {
            config.outputFile = argv[++i];
        } else if (arg == "-s" && i + 1 < argc) {
            config.seedPhrase = argv[++i];
        } else if (arg == "-g" && i + 1 < argc) {
            std::string gridSize = argv[++i];
            std::stringstream ss(gridSize);
            std::string x, y;
            if (std::getline(ss, x, ',') && std::getline(ss, y)) {
                config.gpu.gridSizeX = std::stoi(x);
                config.gpu.gridSizeY = std::stoi(y);
            }
        } else if (arg[0] != '-') {
            // 这是搜索模式
            config.patterns.push_back(arg);
        }
    }
    
    return config;
}

// 结果回调函数
void onResultFound(const SearchResult& result) {
    std::cout << std::endl;
    std::cout << "🎉 找到匹配地址!" << std::endl;
    std::cout << "地址: " << result.address << std::endl;
    std::cout << "私钥 (HEX): " << result.privateKey << std::endl;
    std::cout << "私钥 (WIF): " << result.privateKeyWIF << std::endl;
    std::cout << "公钥: " << result.publicKey << std::endl;
    std::cout << "迭代次数: " << TronUtils::formatNumber(result.iterations) << std::endl;
    std::cout << "耗时: " << TronUtils::formatTime(result.elapsedTime) << std::endl;
    std::cout << std::endl;
}

// 进度回调函数
void onProgressUpdate(const PerformanceStats& stats) {
    static auto lastUpdate = std::chrono::steady_clock::now();
    auto now = std::chrono::steady_clock::now();
    
    // 每秒更新一次
    if (std::chrono::duration_cast<std::chrono::seconds>(now - lastUpdate).count() >= 1) {
        std::cout << "\r[" << TronUtils::formatNumber(stats.keysPerSecond) << " keys/s]"
                  << "[总计 " << TronUtils::formatNumber(stats.totalKeys) << "]"
                  << "[概率 " << std::fixed << std::setprecision(2) << stats.probability << "%]"
                  << "[找到 " << stats.foundCount << "]" << std::flush;
        lastUpdate = now;
    }
}

int main(int argc, char* argv[]) {
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    try {
        // 解析命令行参数
        SearchConfig config = parseArguments(argc, argv);
        
        // 检查是否指定了搜索模式
        if (config.patterns.empty()) {
            std::cout << "错误: 请指定搜索模式" << std::endl;
            std::cout << "使用 -h 查看帮助信息" << std::endl;
            return 1;
        }
        
        // 显示启动信息
        std::cout << "TRON 靓号地址生成器 v" << TRON_VANITY_VERSION << std::endl;
        std::cout << "搜索模式: ";
        for (const auto& pattern : config.patterns) {
            std::cout << pattern << " ";
        }
        std::cout << std::endl;
        
        if (config.caseInsensitive) {
            std::cout << "大小写不敏感搜索" << std::endl;
        }
        
        if (config.useGPU) {
            std::cout << "使用GPU加速" << std::endl;
        } else {
            std::cout << "使用CPU计算" << std::endl;
        }
        
        std::cout << std::endl;
        
        // 创建搜索实例
        g_search = std::make_unique<TronVanitySearch>();
        
        // 设置回调函数
        g_search->setResultCallback(onResultFound);
        g_search->setProgressCallback(onProgressUpdate);
        
        // 配置搜索参数
        if (!g_search->configure(config)) {
            std::cout << "搜索配置失败" << std::endl;
            return 1;
        }
        
        // 开始搜索
        std::cout << "开始搜索..." << std::endl;
        if (!g_search->startSearch()) {
            std::cout << "搜索启动失败" << std::endl;
            return 1;
        }
        
        // 等待搜索完成或用户中断
        while (g_running) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            auto stats = g_search->getStats();
            if (config.stopOnFirst && stats.foundCount > 0) {
                break;
            }
        }
        
        // 停止搜索
        g_search->stopSearch();
        
        // 显示最终统计
        auto finalStats = g_search->getStats();
        std::cout << std::endl;
        std::cout << "搜索完成!" << std::endl;
        std::cout << "总计生成: " << TronUtils::formatNumber(finalStats.totalKeys) << " 个密钥" << std::endl;
        std::cout << "找到地址: " << finalStats.foundCount << " 个" << std::endl;
        std::cout << "平均速度: " << TronUtils::formatNumber(finalStats.keysPerSecond) << " keys/s" << std::endl;
        
        // 保存结果到文件
        if (!config.outputFile.empty()) {
            auto results = g_search->getResults();
            // TODO: 实现结果保存功能
            std::cout << "结果已保存到: " << config.outputFile << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
