#pragma once

#include <string>
#include <vector>
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>

// 版本信息
#define TRON_VANITY_VERSION "1.0.0"

// TRON地址相关常量
#define TRON_ADDRESS_PREFIX 0x41
#define TRON_ADDRESS_LENGTH 21
#define TRON_BASE58_LENGTH 34
#define PRIVATE_KEY_LENGTH 32
#define PUBLIC_KEY_LENGTH 64

// GPU相关常量
#define DEFAULT_GRID_SIZE_X 256
#define DEFAULT_GRID_SIZE_Y 128
#define DEFAULT_THREADS_PER_BLOCK 256

// 前向声明
class GPUEngine;
class TronAddress;
class PatternMatcher;

// 搜索结果结构
struct SearchResult {
    std::string address;        // TRON地址
    std::string privateKey;     // 私钥 (HEX格式)
    std::string privateKeyWIF;  // 私钥 (WIF格式)
    std::string publicKey;      // 公钥 (HEX格式)
    uint64_t iterations;        // 找到时的迭代次数
    double elapsedTime;         // 耗时(秒)
};

// 搜索配置
struct SearchConfig {
    std::vector<std::string> patterns;  // 搜索模式
    bool caseInsensitive = false;       // 大小写不敏感
    bool stopOnFirst = true;            // 找到第一个就停止
    bool useGPU = true;                 // 使用GPU
    std::vector<int> gpuIds;            // GPU设备ID列表
    int cpuThreads = 0;                 // CPU线程数 (0=自动)
    std::string outputFile;             // 输出文件
    std::string seedPhrase;             // 种子短语
    bool verbose = false;               // 详细输出
    
    // GPU配置
    struct {
        int gridSizeX = DEFAULT_GRID_SIZE_X;
        int gridSizeY = DEFAULT_GRID_SIZE_Y;
        int threadsPerBlock = DEFAULT_THREADS_PER_BLOCK;
    } gpu;
};

// 性能统计
struct PerformanceStats {
    std::atomic<uint64_t> totalKeys{0};     // 总生成密钥数
    std::atomic<uint64_t> keysPerSecond{0}; // 每秒生成密钥数
    std::atomic<uint64_t> gpuKeys{0};       // GPU生成密钥数
    std::atomic<uint64_t> cpuKeys{0};       // CPU生成密钥数
    std::atomic<int> foundCount{0};         // 找到的地址数量
    double startTime = 0.0;                 // 开始时间
    double probability = 0.0;               // 当前概率
    double estimatedTime = 0.0;             // 预估剩余时间
};

// 主搜索类
class TronVanitySearch {
public:
    TronVanitySearch();
    ~TronVanitySearch();
    
    // 配置搜索参数
    bool configure(const SearchConfig& config);
    
    // 开始搜索
    bool startSearch();
    
    // 停止搜索
    void stopSearch();
    
    // 获取搜索结果
    std::vector<SearchResult> getResults() const;
    
    // 获取性能统计
    PerformanceStats getStats() const;
    
    // 设置结果回调
    void setResultCallback(std::function<void(const SearchResult&)> callback);
    
    // 设置进度回调
    void setProgressCallback(std::function<void(const PerformanceStats&)> callback);

private:
    SearchConfig m_config;
    std::vector<SearchResult> m_results;
    PerformanceStats m_stats;
    
    std::unique_ptr<GPUEngine> m_gpuEngine;
    std::unique_ptr<PatternMatcher> m_patternMatcher;
    
    std::vector<std::thread> m_cpuThreads;
    std::atomic<bool> m_running{false};
    std::mutex m_resultsMutex;
    
    std::function<void(const SearchResult&)> m_resultCallback;
    std::function<void(const PerformanceStats&)> m_progressCallback;
    
    // 内部方法
    void cpuWorker(int threadId);
    void updateStats();
    void calculateProbability();
    bool isPatternMatch(const std::string& address);
    void saveResult(const SearchResult& result);
};

// 工具函数
namespace TronUtils {
    // 生成随机私钥
    std::vector<uint8_t> generatePrivateKey();
    
    // 从私钥计算公钥
    std::vector<uint8_t> privateKeyToPublicKey(const std::vector<uint8_t>& privateKey);
    
    // 从公钥生成TRON地址
    std::string publicKeyToAddress(const std::vector<uint8_t>& publicKey);
    
    // 私钥转WIF格式
    std::string privateKeyToWIF(const std::vector<uint8_t>& privateKey);
    
    // 十六进制字符串转换
    std::string bytesToHex(const std::vector<uint8_t>& bytes);
    std::vector<uint8_t> hexToBytes(const std::string& hex);
    
    // Base58Check编码/解码
    std::string base58CheckEncode(const std::vector<uint8_t>& data);
    std::vector<uint8_t> base58CheckDecode(const std::string& encoded);
    
    // 验证TRON地址格式
    bool isValidTronAddress(const std::string& address);
    
    // 获取系统时间(毫秒)
    double getCurrentTime();
    
    // 格式化时间显示
    std::string formatTime(double seconds);
    
    // 格式化数字显示
    std::string formatNumber(uint64_t number);
}
