#include "Secp256k1.h"
#include <cstring>
#include <sstream>
#include <iomanip>
#include <algorithm>

Int256::Int256() {
    setZero();
}

Int256::Int256(uint64_t value) {
    data[0] = value;
    data[1] = data[2] = data[3] = 0;
}

Int256::Int256(const uint8_t* bytes) {
    fromBytes(bytes);
}

Int256::Int256(const std::vector<uint8_t>& bytes) {
    if (bytes.size() != 32) {
        throw std::invalid_argument("Byte array must be 32 bytes");
    }
    fromBytes(bytes.data());
}

// 加法
Int256 Int256::operator+(const Int256& other) const {
    Int256 result;
    uint64_t carry = 0;
    
    for (int i = 0; i < 4; ++i) {
        uint64_t sum = data[i] + other.data[i] + carry;
        result.data[i] = sum;
        carry = (sum < data[i]) ? 1 : 0;
    }
    
    return result;
}

// 减法
Int256 Int256::operator-(const Int256& other) const {
    Int256 result;
    uint64_t borrow = 0;
    
    for (int i = 0; i < 4; ++i) {
        uint64_t diff = data[i] - other.data[i] - borrow;
        result.data[i] = diff;
        borrow = (diff > data[i]) ? 1 : 0;
    }
    
    return result;
}

// 乘法 (简化版本，仅用于小数乘法)
Int256 Int256::operator*(const Int256& other) const {
    Int256 result;
    
    // 使用长乘法算法
    for (int i = 0; i < 4; ++i) {
        if (data[i] == 0) continue;
        
        uint64_t carry = 0;
        for (int j = 0; j < 4 && i + j < 4; ++j) {
            if (other.data[j] == 0) continue;
            
            // 64位乘法，产生128位结果
            __uint128_t product = static_cast<__uint128_t>(data[i]) * other.data[j] + 
                                 result.data[i + j] + carry;
            
            result.data[i + j] = static_cast<uint64_t>(product);
            carry = static_cast<uint64_t>(product >> 64);
        }
    }
    
    return result;
}

// 模运算
Int256 Int256::operator%(const Int256& other) const {
    // 简化的模运算实现
    // 实际应用中应使用更高效的算法
    Int256 dividend = *this;
    Int256 divisor = other;
    
    if (divisor.isZero()) {
        throw std::invalid_argument("Division by zero");
    }
    
    if (*this < divisor) {
        return *this;
    }
    
    // 使用减法实现模运算（效率较低，仅用于演示）
    while (dividend >= divisor) {
        dividend = dividend - divisor;
    }
    
    return dividend;
}

// 复合赋值运算符
Int256& Int256::operator+=(const Int256& other) {
    *this = *this + other;
    return *this;
}

Int256& Int256::operator-=(const Int256& other) {
    *this = *this - other;
    return *this;
}

Int256& Int256::operator*=(const Int256& other) {
    *this = *this * other;
    return *this;
}

Int256& Int256::operator%=(const Int256& other) {
    *this = *this % other;
    return *this;
}

// 比较运算符
bool Int256::operator==(const Int256& other) const {
    return memcmp(data, other.data, sizeof(data)) == 0;
}

bool Int256::operator!=(const Int256& other) const {
    return !(*this == other);
}

bool Int256::operator<(const Int256& other) const {
    for (int i = 3; i >= 0; --i) {
        if (data[i] < other.data[i]) return true;
        if (data[i] > other.data[i]) return false;
    }
    return false;
}

bool Int256::operator>(const Int256& other) const {
    return other < *this;
}

bool Int256::operator<=(const Int256& other) const {
    return !(*this > other);
}

bool Int256::operator>=(const Int256& other) const {
    return !(*this < other);
}

// 位运算
Int256 Int256::operator<<(int shift) const {
    if (shift == 0) return *this;
    if (shift >= 256) return Int256();
    
    Int256 result;
    int wordShift = shift / 64;
    int bitShift = shift % 64;
    
    if (bitShift == 0) {
        // 整字移位
        for (int i = 3; i >= wordShift; --i) {
            result.data[i] = data[i - wordShift];
        }
    } else {
        // 需要跨字移位
        for (int i = 3; i >= wordShift; --i) {
            result.data[i] = data[i - wordShift] << bitShift;
            if (i - wordShift > 0) {
                result.data[i] |= data[i - wordShift - 1] >> (64 - bitShift);
            }
        }
    }
    
    return result;
}

Int256 Int256::operator>>(int shift) const {
    if (shift == 0) return *this;
    if (shift >= 256) return Int256();
    
    Int256 result;
    int wordShift = shift / 64;
    int bitShift = shift % 64;
    
    if (bitShift == 0) {
        // 整字移位
        for (int i = 0; i < 4 - wordShift; ++i) {
            result.data[i] = data[i + wordShift];
        }
    } else {
        // 需要跨字移位
        for (int i = 0; i < 4 - wordShift; ++i) {
            result.data[i] = data[i + wordShift] >> bitShift;
            if (i + wordShift + 1 < 4) {
                result.data[i] |= data[i + wordShift + 1] << (64 - bitShift);
            }
        }
    }
    
    return result;
}

Int256& Int256::operator<<=(int shift) {
    *this = *this << shift;
    return *this;
}

Int256& Int256::operator>>=(int shift) {
    *this = *this >> shift;
    return *this;
}

// 工具方法
bool Int256::isZero() const {
    return data[0] == 0 && data[1] == 0 && data[2] == 0 && data[3] == 0;
}

bool Int256::isOdd() const {
    return (data[0] & 1) != 0;
}

int Int256::getBitLength() const {
    for (int i = 3; i >= 0; --i) {
        if (data[i] != 0) {
            // 找到最高非零字，计算其位长度
            uint64_t word = data[i];
            int bitLength = i * 64;
            
            while (word > 0) {
                word >>= 1;
                bitLength++;
            }
            
            return bitLength;
        }
    }
    return 0;
}

void Int256::setZero() {
    memset(data, 0, sizeof(data));
}

void Int256::setOne() {
    data[0] = 1;
    data[1] = data[2] = data[3] = 0;
}

// 转换方法
std::vector<uint8_t> Int256::toBytes() const {
    std::vector<uint8_t> bytes(32);
    
    for (int i = 0; i < 4; ++i) {
        uint64_t word = data[i];
        for (int j = 0; j < 8; ++j) {
            bytes[i * 8 + j] = static_cast<uint8_t>(word >> (j * 8));
        }
    }
    
    // 转换为大端序
    std::reverse(bytes.begin(), bytes.end());
    return bytes;
}

std::string Int256::toHex() const {
    std::vector<uint8_t> bytes = toBytes();
    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    
    for (uint8_t byte : bytes) {
        ss << std::setw(2) << static_cast<int>(byte);
    }
    
    return ss.str();
}

void Int256::fromBytes(const uint8_t* bytes) {
    // 输入是大端序，需要转换为小端序
    for (int i = 0; i < 4; ++i) {
        uint64_t word = 0;
        for (int j = 0; j < 8; ++j) {
            word |= static_cast<uint64_t>(bytes[31 - (i * 8 + j)]) << (j * 8);
        }
        data[i] = word;
    }
}

void Int256::fromHex(const std::string& hex) {
    if (hex.length() != 64) {
        throw std::invalid_argument("Hex string must be 64 characters");
    }
    
    std::vector<uint8_t> bytes(32);
    for (int i = 0; i < 32; ++i) {
        std::string byteStr = hex.substr(i * 2, 2);
        bytes[i] = static_cast<uint8_t>(std::stoul(byteStr, nullptr, 16));
    }
    
    fromBytes(bytes.data());
}

// 模运算方法
Int256 Int256::modAdd(const Int256& other, const Int256& mod) const {
    Int256 result = *this + other;
    if (result >= mod) {
        result = result - mod;
    }
    return result;
}

Int256 Int256::modSub(const Int256& other, const Int256& mod) const {
    if (*this >= other) {
        return *this - other;
    } else {
        return mod - (other - *this);
    }
}

Int256 Int256::modMul(const Int256& other, const Int256& mod) const {
    // 简化实现，实际应用中需要更高效的算法
    Int256 result = *this * other;
    return result % mod;
}

Int256 Int256::modInv(const Int256& mod) const {
    // 使用扩展欧几里得算法计算模逆
    // 这里提供简化实现
    throw std::runtime_error("modInv not implemented");
}

Int256 Int256::modPow(const Int256& exp, const Int256& mod) const {
    // 快速幂算法
    Int256 result;
    result.setOne();
    Int256 base = *this % mod;
    Int256 exponent = exp;
    
    while (!exponent.isZero()) {
        if (exponent.isOdd()) {
            result = result.modMul(base, mod);
        }
        base = base.modMul(base, mod);
        exponent = exponent >> 1;
    }
    
    return result;
}
